<template>
  <AuthDialog
    :model-value="modelValue"
    @update:model-value="updateModelValue"
    @close="handleClose"
    title="Join Our Innovation Community"
    subtitle="Connect with innovators, mentors, and industry experts"
    :show-category-section="true"
    :show-sign-in-section="!!selectedCategory"
    class="unified-signup-modal"
  >
    <!-- Category Selection Section -->
    <template #category-section>
      <CategorySelectionGrid
        :selected-category-id="selectedCategory?.id"
        @category-selected="handleCategorySelected"
      />
    </template>

    <!-- Sign-In Method Section -->
    <template #signin-section>
      <SignInMethodSelection
        :enabled="!!selectedCategory"
        :loading="loading"
        @method-selected="handleMethodSelected"
        @form-submit="handleFormSubmit"
        @form-cancel="handleFormCancel"
      />
    </template>

    <!-- Footer -->
    <template #footer>
      <div class="modal-footer">
        <div class="footer-info">
          <p class="terms-text">
            By signing up, you agree to our 
            <a href="/terms" target="_blank" class="link">Terms of Service</a> 
            and 
            <a href="/privacy" target="_blank" class="link">Privacy Policy</a>
          </p>
        </div>
        
        <div class="footer-actions">
          <q-btn
            flat
            no-caps
            color="grey-7"
            @click="switchToSignIn"
            class="switch-btn"
          >
            Already have an account? Sign In
          </q-btn>
        </div>
      </div>
    </template>
  </AuthDialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import AuthDialog from './AuthDialog.vue'
import CategorySelectionGrid from './CategorySelectionGrid.vue'
import SignInMethodSelection from './SignInMethodSelection.vue'
import { useCategoryService, type CategoryOption } from '@/services/categoryService'
import { useUnifiedAuth } from '@/services/unifiedAuthService'

interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'close': []
  'switch-to-signin': []
}>()

const { saveSelectedCategory } = useCategoryService()
const { handleSignUp, signUpFormData, updateSignUpForm } = useUnifiedAuth()

const selectedCategory = ref<CategoryOption | null>(null)
const loading = ref(false)

const updateModelValue = (value: boolean) => {
  emit('update:modelValue', value)
}

const handleClose = () => {
  emit('close')
}

const switchToSignIn = () => {
  emit('switch-to-signin')
}

const handleCategorySelected = (category: CategoryOption) => {
  selectedCategory.value = category
  saveSelectedCategory(category.id)
  updateSignUpForm('selectedCategory', category)
}

const handleMethodSelected = (method: string) => {
  console.log('Sign-in method selected:', method)
  // Method selection is handled by the SignInMethodSelection component
}

const handleFormSubmit = async (formData: any) => {
  if (!selectedCategory.value) {
    console.error('No category selected')
    return
  }

  loading.value = true
  
  try {
    // Update form data with selected category
    updateSignUpForm('email', formData.email)
    updateSignUpForm('password', formData.password)
    updateSignUpForm('acceptTerms', formData.acceptTerms)
    updateSignUpForm('selectedCategory', selectedCategory.value)
    
    // Trigger sign up
    await handleSignUp()
    
    // Success is handled by the auth service
    // Modal will be closed automatically
  } catch (error) {
    console.error('Sign up error:', error)
  } finally {
    loading.value = false
  }
}

const handleFormCancel = () => {
  // Form was cancelled, stay in the modal
  console.log('Form cancelled')
}
</script>

<style scoped>
.unified-signup-modal {
  z-index: 9500;
}

.modal-footer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
}

.footer-info {
  text-align: center;
}

.terms-text {
  margin: 0;
  font-size: 0.85rem;
  color: #666;
  line-height: 1.4;
}

.link {
  color: #0D8A3E;
  text-decoration: none;
  font-weight: 500;
}

.link:hover {
  text-decoration: underline;
}

.footer-actions {
  display: flex;
  justify-content: center;
}

.switch-btn {
  font-size: 0.9rem;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.switch-btn:hover {
  background: rgba(0, 0, 0, 0.05);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .modal-footer {
    gap: 0.75rem;
  }
  
  .terms-text {
    font-size: 0.8rem;
  }
  
  .switch-btn {
    font-size: 0.85rem;
    padding: 0.375rem 0.75rem;
  }
}

@media (max-width: 480px) {
  .footer-actions {
    width: 100%;
  }
  
  .switch-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
