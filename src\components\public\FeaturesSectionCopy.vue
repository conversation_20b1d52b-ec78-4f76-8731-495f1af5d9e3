<template>
  <section class="features-section">
    <div class="background-overlay"></div>
    <div class="container q-mx-auto q-px-md">
      <div class="row">
        <div class="col-1" />
        <div class="col-10">
          <div class="text-center">
            <div class="section-header">
              <h2 class="section-title">Platform Features</h2>
              <p class="section-description">
                Explore the powerful tools and features that make our innovation ecosystem unique and effective.
              </p>
            </div>
          </div>
          
          <div class="features-grid">
            <div
              v-for="(feature, index) in features"
              :key="index"
              class="feature-card"
              :style="{ animationDelay: `${index * 0.1}s` }"
            >
              <div class="card-content">
                <div class="feature-icon-container">
                  <q-icon :name="feature.icon" size="3rem" color="white" class="feature-icon" />
                </div>
                <h3 class="feature-title">{{ feature.title }}</h3>
                <p class="feature-description">{{ feature.description }}</p>
              </div>
            </div>
          </div>
          
          <div class="text-center q-mt-xl">
            <button class="cta-button" @click="scrollToSignup">
              Explore All Features
            </button>
          </div>
        </div>
        <div class="col-1" />
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const features = ref([
  {
    icon: 'people',
    title: 'Community Networking',
    description: 'Connect with entrepreneurs, investors, mentors, and industry experts in a vibrant community.'
  },
  {
    icon: 'school',
    title: 'Learning Resources',
    description: 'Access a wealth of educational content and training opportunities to grow your skills.'
  },
  {
    icon: 'trending_up',
    title: 'Growth Analytics',
    description: 'Track your progress and measure success with comprehensive analytics and insights.'
  },
  {
    icon: 'handshake',
    title: 'Partnership Matching',
    description: 'Find the perfect partners, collaborators, and team members for your projects.'
  },
  {
    icon: 'event',
    title: 'Events & Workshops',
    description: 'Participate in exclusive events, workshops, and networking opportunities.'
  },
  {
    icon: 'support',
    title: '24/7 Support',
    description: 'Get help when you need it with our dedicated support team and community.'
  }
])

const scrollToSignup = () => {
  const signupSection = document.getElementById('signup-section')
  if (signupSection) {
    signupSection.scrollIntoView({ behavior: 'smooth' })
  }
}
</script>

<style scoped>
.features-section {
  position: relative;
  padding: 80px 0;
  background-image: url('@/assets/hero/businessman-businesswoman-bumping-their-fist-front-corporate-building.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  overflow: hidden;
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(131, 186, 38, 0.85) 0%, 
    rgba(13, 138, 62, 0.75) 50%,
    rgba(131, 186, 38, 0.85) 100%);
  z-index: 1;
}

.container {
  position: relative;
  z-index: 2;
}

.section-header {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 60px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 20px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.section-description {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 40px;
}

.feature-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: translateY(30px);
  animation: slideUp 0.6s ease forwards;
}

.feature-card:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
}

.card-content {
  text-align: center;
  color: white;
}

.feature-icon-container {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  backdrop-filter: blur(10px);
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: white;
}

.feature-description {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
}

.cta-button {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
  border: 2px solid white;
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  padding: 16px 32px;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.cta-button:hover {
  background: white;
  color: #83BA26;
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
}

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .features-section {
    padding: 60px 0;
    background-attachment: scroll;
  }

  .section-header {
    padding: 30px 20px;
    margin-bottom: 40px;
  }

  .section-title {
    font-size: 2rem;
  }

  .section-description {
    font-size: 1rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .feature-card {
    padding: 25px;
    /* Ensure visibility on mobile */
    opacity: 1 !important;
    transform: translateY(0) !important;
    animation: none;
  }

  .feature-title {
    font-size: 1.3rem;
  }

  .cta-button {
    font-size: 1rem;
    padding: 14px 28px;
  }
}

@media (max-width: 480px) {
  .section-header {
    padding: 20px 15px;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .feature-card {
    padding: 20px;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
