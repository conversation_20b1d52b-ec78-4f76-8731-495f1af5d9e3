<template>
  <q-page class="q-pa-md">
    <!-- Full-page loading overlay -->
    <div v-if="initialLoading" class="full-page-loader">
      <q-spinner-dots color="primary" size="80px" />
      <div class="q-mt-md text-subtitle1">Loading profile data...</div>
    </div>

    <!-- New User Welcome Component for users without a profile -->
    <div v-if="!initialLoading && isNewUser">
      <div class="q-pa-md text-center">
        <q-card class="q-pa-md">
          <q-card-section>
            <div class="text-h6">Welcome to the Innovation Hub!</div>
            <p>You need to create a profile to continue.</p>
            <q-btn color="light-green-8" label="Create Profile" :to="{ name: 'profile-create' }" />
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Main content - only show when not in initial loading state and user has a profile -->
    <div v-else-if="!initialLoading" class="row q-col-gutter-md">
      <div class="col-12">
        <q-card>
          <q-card-section class="bg-light-green-8 text-white">
            <div class="row items-center">
              <div class="col">
                <div class="text-h6">
                  Edit Profile
                  <q-badge color="light-green-5" class="q-ml-sm">
                    Edit Existing Profile
                  </q-badge>
                </div>
                <div class="text-subtitle2">{{ pageSubtitle }}</div>
              </div>
              <div class="col-auto">
                <q-btn
                  flat
                  round
                  color="white"
                  :to="{ name: 'profile-view', params: { id: profile?.user_id } }"
                  v-if="profile?.user_id"
                >
                  <unified-icon name="visibility" color="white" />

                  <q-tooltip>View Profile</q-tooltip>
                </q-btn>
              </div>
            </div>
          </q-card-section>



          <q-card-section>
            <q-stepper
              v-model="step"
              vertical
              color="light-green-8"
              animated
              :contracted="$q.screen.lt.md"
              alternative-labels
            >
              <!-- Step 1: Profile Information -->
              <q-step
                :name="1"
                title="Profile Information"
                :done="step > 1"
                prefix="1"
              >
                <!-- Step loading indicator -->
                <div v-if="loading && step === 1" class="step-loading">
                  <q-spinner color="primary" size="40px" />
                  <div class="q-mt-sm">Loading profile information...</div>
                </div>

                <!-- Step content - only show when not loading -->
                <div v-show="!loading || step !== 1">
                  <div class="q-pa-md">
                    <p>Update your profile information.</p>



                  <!-- Profile Type Display (read-only) -->
                  <div class="q-mb-md">
                    <div class="text-subtitle1 q-mb-sm">Profile Category: <q-badge color="negative" class="q-ml-sm">Cannot be changed</q-badge></div>
                    <q-chip
                      color="primary"
                      text-color="white"
                      icon="category"
                      class="readonly-chip"
                      outline
                    >
                      {{ formatProfileType(profile.profile_type) }}
                    </q-chip>
                    <div class="text-caption q-mt-sm text-grey-8">
                      Your profile category determines what information we collect and cannot be changed after profile creation.
                    </div>
                  </div>
                </div>

                  <q-stepper-navigation>
                    <q-btn
                    color="light-green-8"
                    label="Continue"
                    @click="step = 2"
                    :loading="loading"
                  >
                    <template v-slot:append>
                      <unified-icon name="arrow-right" />
                    </template>
                  </q-btn>
                  <q-btn
                    flat
                    color="grey"
                    label="Cancel"
                    class="q-ml-sm"
                    to="/dashboard"
                  >
                    <template v-slot:append>
                      <unified-icon name="close" />
                    </template>
                    </q-btn>
                  </q-stepper-navigation>
                </div> <!-- End of step 1 content -->
              </q-step>

              <!-- Step 2: Personal Details -->
              <q-step
                :name="2"
                title="Personal Details"
                :done="step > 2"
                prefix="2"
              >
                <!-- Step loading indicator -->
                <div v-if="loading && step === 2" class="step-loading">
                  <q-spinner color="primary" size="40px" />
                  <div class="q-mt-sm">Loading personal details...</div>
                </div>

                <!-- Step content - only show when not loading -->
                <div v-show="!loading || step !== 2">
                  <div class="q-pa-md">
                    <p>These details will be used across all your profiles.</p>

                    <div class="row q-col-gutter-md q-mt-md">
                      <div class="col-12 col-md-6">
                        <q-input
                        v-model="formState.first_name"
                        label="First Name *"
                        outlined
                        :rules="[(val: string) => !!val || 'First name is required']"
                        @update:model-value="updateField('first_name', $event)"
                      />
                    </div>

                    <div class="col-12 col-md-6">
                      <q-input
                        v-model="formState.last_name"
                        label="Last Name *"
                        outlined
                        :rules="[(val: string) => !!val || 'Last name is required']"
                        @update:model-value="updateField('last_name', $event)"
                      />
                    </div>

                    <div class="col-12 col-md-6">
                      <q-input
                        v-model="formState.email"
                        label="Email *"
                        outlined
                        type="email"
                        readonly
                        disable
                      />
                    </div>

                    <div class="col-12 col-md-6">
                      <phone-number-input
                        v-model="phoneModel"
                        @update:model-value="updatePhone"
                      />
                    </div>

                    <div class="col-12 col-md-6">
                      <q-select
                        v-model="formState.gender"
                        :options="genderOptions"
                        label="Gender"
                        outlined
                        emit-value
                        map-options
                        @update:model-value="updateField('gender', $event)"
                      />
                    </div>


                  </div>
                </div>

                  <q-stepper-navigation>
                    <q-btn
                    color="light-green-8"
                    label="Continue"
                    @click="validateAndContinue"
                    :loading="loading"
                    :disable="!canContinueFromPersonalDetails"
                  >
                    <template v-slot:append>
                      <unified-icon name="arrow-right" />
                    </template>
                  </q-btn>
                  <q-btn
                    flat
                    color="light-green-8"
                    label="Back"
                    class="q-ml-sm"
                    @click="step = 1"
                  >
                    <template v-slot:prepend>
                      <unified-icon name="arrow_back" />
                    </template>
                    </q-btn>
                  </q-stepper-navigation>
                </div> <!-- End of step 2 content -->
              </q-step>

              <!-- Step 3: Profile-Specific Questions -->
              <q-step
                :name="3"
                title="Profile Details"
                prefix="3"
              >
                <!-- Step loading indicator -->
                <div v-if="loading && step === 3" class="step-loading">
                  <q-spinner color="primary" size="40px" />
                  <div class="q-mt-sm">Loading profile details...</div>
                </div>

                <!-- Step content - only show when not loading -->
                <div v-show="!loading || step !== 3">
                  <div class="q-pa-md">
                    <p>Please provide information specific to your {{ formatProfileType(profile.profile_type) }} profile.</p>

                    <!-- Dynamic Profile Form Component -->
                    <dynamic-profile-form
                      v-if="profile.profile_type"
                      :profile-type="profile.profile_type"
                      v-model="formState"
                      :loading="loading"
                      :is-new-profile="isNewProfileComputed"
                      @save="saveProfileData"
                      @update:model-value="updateFields"
                    />

                    <!-- Default message for unsupported profile types -->
                    <div v-else class="q-mt-md">
                      <p class="text-italic">Please select a profile type to continue.</p>
                    </div>
                  </div>

                  <q-stepper-navigation>
                    <q-btn
                      color="light-green-8"
                      label="Submit"
                      @click="submitProfile"
                      :loading="loading"
                    >
                      <template v-slot:append>
                        <unified-icon name="check_circle" />
                      </template>
                    </q-btn>
                    <q-btn
                      flat
                      color="light-green-8"
                      label="Back"
                      class="q-ml-sm"
                      @click="step = 2"
                    >
                      <template v-slot:prepend>
                        <unified-icon name="arrow_back" />
                      </template>
                    </q-btn>
                  </q-stepper-navigation>
                </div> <!-- End of step 3 content -->
              </q-step>
            </q-stepper>
          </q-card-section>
        </q-card>
      </div>
    </div>

    <!-- Status indicators -->
    <q-page-sticky position="bottom-right" :offset="[18, 18]">
      <!-- Auto-save indicator for edit mode -->
      <q-badge color="light-green-8" v-if="showSavedIndicator">
        <unified-icon name="check" size="xs" />
        Changes saved automatically
      </q-badge>
    </q-page-sticky>


  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { useProfileStore, type BaseProfile } from '../../stores/profile'
import { useNotificationStore } from '../../stores/notifications'
import DynamicProfileForm from '../../components/profile/DynamicProfileForm.vue'
import ProfileCompletionStatus from '../../components/profile/ProfileCompletionStatus.vue'
import UnifiedIcon from '../../components/ui/UnifiedIcon.vue'
import { useLoadingState } from '../../services/loadingStateService'
import { getProfileTypeOptions } from '../../services/profileTypes'
import PhoneNumberInput from '../../components/ui/PhoneNumberInput.vue'
import { useUserState } from '../../services/userStateService'
import { formatProfileType as formatProfileTypeUtil } from '../../services/profileUtils'
import { useProfileFormService } from '../../services/profileFormService'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const profileStore = useProfileStore()
const notifications = useNotificationStore()

// Use the user state service to check if this is a new user
const { isNewUser, checkUserState } = useUserState()

// Use the profile form service for centralized state management
const {
  formState,
  hasUnsavedChanges,
  isSaving,
  initializeForm,
  updateField,
  updateFields,
  saveForm,
  autoSave,
  profileType,
  profileId,
  profileCompletion
} = useProfileFormService()

// State
const initialLoading = ref(true) // Loading state for initial data fetch
const step = ref(1) // Default to step 1 (Profile Category)
const profile = computed(() => profileStore.currentProfile || {} as BaseProfile)

// Flags to track profile state
const profileExists = computed(() => !!profile.value?.user_id)
const profileCreatedInDatabase = computed(() => !!profile.value?.user_id)
const showSavedIndicator = ref(false)

// For phone input component
const phoneModel = computed(() => ({
  countryCode: formState.value.phone_country_code || '',
  number: formState.value.phone_number || ''
}))

const genderOptions = [
  { label: 'Male', value: 'male' },
  { label: 'Female', value: 'female' }
]

// Use the loading state service
const { isLoading, withLoading } = useLoadingState()
const loading = computed(() => isLoading('profileEdit') || isSaving.value)

// Options for select fields from centralized service
const profileTypeOptions = getProfileTypeOptions()

// Computed
const pageSubtitle = computed(() => {
  if (profile.value?.profile_type) {
    return `${formatProfileType(profile.value.profile_type)} Profile`
  }
  return 'Update your profile information'
})

// Computed property to determine if this is a new profile
const isNewProfileComputed = computed(() => {
  return !profileExists.value
})

// Computed property to determine if we're creating a new profile
const isCreatingNewProfile = computed(() => {
  return isNewProfileComputed.value && !profileCreatedInDatabase.value
})

// Computed property to determine if continue button should be enabled
const canContinueFromPersonalDetails = computed(() => {
  return !!(formState.value.first_name && formState.value.last_name)
})

// Methods
function formatProfileType(type: string | null | undefined): string {
  return formatProfileTypeUtil(type) || 'No Type'
}

function getProfileTypeDescription(type: string): string {
  const descriptions: Record<string, string> = {
    'innovator': 'Entrepreneurs, startups, and innovators with ideas or products.',
    'investor': 'Angel investors, VCs, and investment firms looking for opportunities.',
    'mentor': 'Experienced professionals offering guidance and mentorship.',
    'professional': 'Industry professionals looking to network and collaborate.',
    'industry_expert': 'Subject matter experts with specialized knowledge.',
    'academic_student': 'Students looking for opportunities and connections.',
    'academic_institution': 'Universities, colleges, and research institutions.',
    'organisation': 'Companies, NGOs, and other organizations.'
  }

  return descriptions[type] || ''
}

// Save function for profile data
async function saveProfileData(): Promise<void> {
  // Only save if the profile exists in the database and has a user_id
  if (!profileExists.value) {
    return
  }

  try {
    // Use the form service to save all profile data
    const success = await saveForm()

    if (success) {
      // Show saved indicator
      showSavedIndicator.value = true
      setTimeout(() => {
        showSavedIndicator.value = false
      }, 3000)
    }
  } catch (error) {
    notifications.error('Failed to save profile: ' + (error as Error).message)
  }
}

// Update phone number from phone input component
function updatePhone(value: { countryCode: string, number: string }): void {
  updateField('phone_country_code', value.countryCode)
  updateField('phone_number', value.number)
}

// Validate personal details and continue to next step
async function validateAndContinue(): Promise<void> {
  // Validate required fields
  if (!formState.value.first_name || !formState.value.last_name) {
    notifications.warning('Please fill in all required fields')
    return
  }

  // Save changes before continuing
  await saveProfileData()

  // Move to next step
  step.value = 3
}

// Submit the profile
async function submitProfile(): Promise<void> {
  await withLoading('profileEdit', async () => {
    try {
      // Save all profile data
      await saveProfileData()

      // Show success message
      notifications.success('Profile updated successfully')

      // Redirect to profile view
      if (profile.value.user_id) {
        router.push({ name: 'profile-view', params: { id: profile.value.user_id } })
      } else {
        router.push({ name: 'dashboard' })
      }
    } catch (error) {
      notifications.error('Failed to submit profile: ' + (error as Error).message)
    }
  })
}

// Load profile data
async function loadProfileData(): Promise<void> {
  await withLoading('profileEdit', async () => {
    try {
      // Get profile ID from route params
      const profileId = route.params.id as string

      if (!profileId) {
        throw new Error('No profile ID provided')
      }

      // Load profile from store
      await profileStore.fetchProfile(profileId)

      // Initialize form with profile data
      initializeForm()

      // Check if this is a new user
      await checkUserState()
    } catch (error) {
      notifications.error('Failed to load profile: ' + (error as Error).message)
      router.push({ name: 'dashboard' })
    } finally {
      initialLoading.value = false
    }
  })
}

// Autosave removed to prevent form reloading issues
// Only manual save and save on route change are enabled

// Save before leaving the page
onBeforeRouteLeave(async (to, from, next) => {
  if (hasUnsavedChanges.value) {
    try {
      await saveForm()
      next()
    } catch (error) {
      if (confirm('There was an error saving your profile changes. Continue without saving?')) {
        next()
      } else {
        next(false)
      }
    }
  } else {
    next()
  }
})

// Load profile data on mount
onMounted(async () => {
  await loadProfileData()
})
</script>

<style scoped>
.full-page-loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
}

.step-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.readonly-chip {
  cursor: default;
}
</style>
