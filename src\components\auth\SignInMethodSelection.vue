<template>
  <div class="signin-method-selection">
    <!-- Method Selection Cards -->
    <div class="method-grid">
      <!-- Email/Password Method -->
      <div 
        class="method-card glass-card glass-card--interactive"
        :class="{ 'method-disabled': !enabled }"
        @click="handleMethodSelect('email-password')"
        @keydown.enter="handleMethodSelect('email-password')"
        @keydown.space.prevent="handleMethodSelect('email-password')"
        tabindex="0"
        role="button"
        :aria-label="enabled ? 'Sign up with email and password' : 'Select a category first to enable sign-up methods'"
      >
        <div class="method-content">
          <div class="method-icon">
            <unified-icon name="email" size="2rem" color="#4285F4" />
          </div>
          <h4 class="method-title">Email & Password</h4>
          <p class="method-description">Create an account using your email address and a secure password</p>
          <div class="method-features">
            <div class="feature-item">
              <unified-icon name="security" size="1rem" color="#4285F4" />
              <span>Secure & Private</span>
            </div>
            <div class="feature-item">
              <unified-icon name="speed" size="1rem" color="#4285F4" />
              <span>Quick Setup</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Google Method (Coming Soon) -->
      <div class="method-card glass-card method-disabled">
        <div class="method-content">
          <div class="method-icon">
            <unified-icon name="google" size="2rem" color="#DB4437" />
          </div>
          <h4 class="method-title">Google Account</h4>
          <p class="method-description">Sign up quickly using your existing Google account</p>
          <div class="coming-soon-badge">
            <span>Coming Soon</span>
          </div>
        </div>
      </div>

      <!-- LinkedIn Method (Coming Soon) -->
      <div class="method-card glass-card method-disabled">
        <div class="method-content">
          <div class="method-icon">
            <unified-icon name="linkedin" size="2rem" color="#0077B5" />
          </div>
          <h4 class="method-title">LinkedIn</h4>
          <p class="method-description">Connect using your professional LinkedIn profile</p>
          <div class="coming-soon-badge">
            <span>Coming Soon</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Disabled State Message -->
    <div v-if="!enabled" class="disabled-message glass-card glass-card--medium">
      <unified-icon name="info" size="1.5rem" color="primary" />
      <p>Please select a category above to choose your sign-up method</p>
    </div>

    <!-- Email/Password Form (when selected) -->
    <div v-if="showEmailForm" class="email-form-container">
      <EmailPasswordForm
        mode="signup"
        :loading="loading"
        @submit="handleFormSubmit"
        @cancel="handleFormCancel"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import UnifiedIcon from '@/components/ui/UnifiedIcon.vue'
import EmailPasswordForm from './EmailPasswordForm.vue'

interface Props {
  enabled?: boolean
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  enabled: false,
  loading: false
})

const emit = defineEmits<{
  'method-selected': [method: string]
  'form-submit': [data: any]
  'form-cancel': []
}>()

const showEmailForm = ref(false)

const handleMethodSelect = (method: string) => {
  if (!props.enabled) return
  
  if (method === 'email-password') {
    showEmailForm.value = true
  }
  
  emit('method-selected', method)
}

const handleFormSubmit = (data: any) => {
  emit('form-submit', data)
}

const handleFormCancel = () => {
  showEmailForm.value = false
  emit('form-cancel')
}
</script>

<style scoped>
.signin-method-selection {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 100%;
}

.method-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1rem;
}

.method-card {
  padding: 1.5rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.method-card:not(.method-disabled):hover {
  transform: translateY(-2px);
  box-shadow: var(--glass-shadow-hover);
}

.method-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.method-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.method-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3.5rem;
  height: 3.5rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.method-title {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #333;
}

.method-description {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
}

.method-features {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #555;
}

.coming-soon-badge {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.disabled-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  text-align: center;
  border-radius: 12px;
  margin-top: 1rem;
}

.disabled-message p {
  margin: 0;
  font-size: 0.95rem;
  color: #666;
}

.email-form-container {
  margin-top: 1.5rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .method-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .method-card {
    padding: 1.25rem;
  }
  
  .method-title {
    font-size: 1rem;
  }
  
  .method-description {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .method-card {
    padding: 1rem;
  }
  
  .method-icon {
    width: 3rem;
    height: 3rem;
  }
  
  .disabled-message {
    padding: 0.75rem 1rem;
  }
  
  .email-form-container {
    padding: 1rem;
  }
}
</style>
