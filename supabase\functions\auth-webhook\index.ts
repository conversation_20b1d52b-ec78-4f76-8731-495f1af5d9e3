// Supabase Auth Webhook Handler for user events
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { corsHeaders } from '../_shared/cors.ts'

// Resend API configuration
const RESEND_API_KEY = Deno.env.get('RESEND_KEY')
const DEFAULT_FROM_EMAIL = Deno.env.get('SENDGRID_FROM_EMAIL') || '<EMAIL>'
const DEFAULT_FROM_NAME = Deno.env.get('SENDGRID_FROM_NAME') || 'ZB Innovation Hub'
const SITE_URL = Deno.env.get('SITE_URL') || 'https://zbinnovation.co.zw'

// Function to extract name from email
function extractNameFromEmail(email: string): string | undefined {
  if (!email) return undefined

  // Get the part before the @ symbol
  const localPart = email.split('@')[0]

  // Remove numbers and special characters
  const cleanedName = localPart.replace(/[0-9_.-]/g, ' ')

  // Capitalize first letter of each word
  const words = cleanedName.split(' ')
    .filter(word => word.length > 0)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())

  // Return the first word if it exists
  return words.length > 0 ? words[0] : undefined
}

// Function to strip HTML tags for plain text version
function stripHtml(html: string): string {
  return html
    .replace(/<[^>]*>/g, '')
    .replace(/\s+/g, ' ')
    .trim()
}

// Function to send email via Resend
async function sendEmail(to: string, subject: string, html: string, text?: string): Promise<Response> {
  if (!RESEND_API_KEY) {
    throw new Error('Resend API key is not configured')
  }

  console.log(`Sending email to ${to} with subject "${subject}"`)

  const url = 'https://api.resend.com/emails'
  const plainText = text || stripHtml(html)

  const payload = {
    from: `${DEFAULT_FROM_NAME} <${DEFAULT_FROM_EMAIL}>`,
    to: [to],
    subject: subject,
    html: html,
    text: plainText
  }

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${RESEND_API_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(payload)
  })

  if (!response.ok) {
    let errorMessage = `Failed to send email: ${response.status} ${response.statusText}`
    try {
      const errorData = await response.json()
      errorMessage = `${errorMessage} - ${JSON.stringify(errorData)}`
    } catch (e) {
      // Ignore JSON parsing error
    }
    throw new Error(errorMessage)
  }

  return response
}

// Function to generate welcome email
function generateWelcomeEmail(email: string, firstName?: string): { html: string; subject: string } {
  // Try to extract name from email if not provided
  const extractedName = !firstName ? extractNameFromEmail(email) : undefined
  const greeting = firstName 
    ? `Hi ${firstName},` 
    : extractedName 
      ? `Hi ${extractedName},` 
      : 'Hi there,'

  const subject = 'Welcome to ZB Innovation Hub'

  const html = `
    <div style="max-width: 600px; margin: 0 auto; padding: 24px; font-family: Arial, sans-serif;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://zbinnovation.com/smile-factory-logo.svg" alt="ZB Innovation Hub" style="max-width: 200px;">
      </div>

      <h1 style="color: #0D8A3E; margin-bottom: 16px;">${greeting}</h1>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        Thank you for joining the ZB Innovation Hub platform. We're excited to have you on board!
      </p>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        Our platform connects innovators, mentors, organizations, and academic institutions to foster collaboration and drive innovation in Zimbabwe.
      </p>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        To get started, please complete your profile to unlock all the features of our platform:
      </p>

      <div style="text-align: center; margin: 32px 0;">
        <a href="${SITE_URL}/dashboard" 
           style="background-color: #0D8A3E; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; margin-right: 10px;">
          Go to Dashboard
        </a>
        <a href="${SITE_URL}/dashboard/profile" 
           style="background-color: #a4ca39; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">
          Complete Profile
        </a>
      </div>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        If you have any questions or need assistance, please don't hesitate to contact our support team.
      </p>

      <p style="margin-bottom: 8px; line-height: 1.5;">
        Best regards,
      </p>

      <p style="line-height: 1.5;">
        The ZbInnovation Team
      </p>

      <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
        <p>
          © ${new Date().getFullYear()} ZbInnovation. All rights reserved.
        </p>
        <p>
          This email was sent to ${email}
        </p>
      </div>
    </div>
  `

  return { html, subject }
}

// Main handler function
serve(async (req) => {
  console.log('Auth Webhook Function invoked:', new Date().toISOString())

  // Handle CORS for preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Parse the webhook payload
    const payload = await req.json()
    console.log('Received webhook payload:', JSON.stringify(payload))

    // Extract user data from the webhook payload
    // The structure depends on how Supabase sends the webhook data
    // This is the standard format for Supabase Auth webhooks
    const user = payload.record || payload.user
    
    if (!user || !user.email) {
      console.error('Invalid webhook payload - missing user data')
      return new Response(
        JSON.stringify({ error: 'Invalid webhook payload - missing user data' }),
        { 
          status: 400,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        }
      )
    }

    // Welcome email is now handled by the application's emailService
    // to prevent duplicate emails and ensure proper configuration
    console.log('Welcome email will be handled by application emailService for:', user.email)

    // Return success response
    return new Response(
      JSON.stringify({
        success: true,
        message: `User signup processed, welcome email handled by application`
      }),
      {
        status: 200,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    )
  } catch (error) {
    console.error('Error processing webhook:', error)
    
    return new Response(
      JSON.stringify({ 
        success: false, 
        error: error.message || 'Failed to process webhook' 
      }),
      {
        status: 500,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      }
    )
  }
})
