<template>
  <q-layout view="hHh lpR lFf">
    <!-- Mobile Navigation Drawer -->
    <q-drawer
      v-model="leftDrawerOpen"
      bordered
      :width="250"
      class="bg-blacki text-white mobile-nav-drawer"
      side="left"
      behavior="mobile"
    >
      <q-scroll-area class="fit">
        <q-list padding>
          <q-item-label header class="text-primary text-weight-bold">Navigation</q-item-label>

          <!-- Home Link -->
          <q-item clickable v-ripple to="/" exact active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="home" color="primary" />
            </q-item-section>
            <q-item-section>Home</q-item-section>
          </q-item>

          <!-- About Link -->
          <q-item clickable v-ripple to="/about" active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="info" color="primary" />
            </q-item-section>
            <q-item-section>About</q-item-section>
          </q-item>

          <!-- Innovation Community Link -->
          <q-item clickable v-ripple @click="navigateTo('/innovation-community?tab=feed')" active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="people" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Innovation Community</q-item-label>
              <q-item-label caption>Explore our innovation community</q-item-label>
            </q-item-section>
            <q-item-section side>
              <q-badge color="accent" text-color="white" rounded>NEW</q-badge>
            </q-item-section>
          </q-item>

          <!-- Contact Us Link -->
          <q-item clickable v-ripple to="/contact-us" active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="contact_support" color="primary" />
            </q-item-section>
            <q-item-section>Contact Us</q-item-section>
          </q-item>

          <!-- Legal & Support Section -->
          <q-separator class="q-my-md" />
          <q-item-label header class="text-primary text-weight-bold">Legal & Support</q-item-label>

          <!-- Privacy Policy -->
          <q-item clickable v-ripple to="/legal/privacy-policy" active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="privacy_tip" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Privacy Policy</q-item-label>
              <q-item-label caption>Data protection & privacy</q-item-label>
            </q-item-section>
          </q-item>

          <!-- Terms & Conditions -->
          <q-item clickable v-ripple to="/legal/terms-conditions" active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="gavel" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Terms & Conditions</q-item-label>
              <q-item-label caption>Platform usage guidelines</q-item-label>
            </q-item-section>
          </q-item>

          <!-- GDPR & Cookies -->
          <q-item clickable v-ripple to="/legal/gdpr-compliance" active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="security" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>GDPR & Cookies</q-item-label>
              <q-item-label caption>Data compliance & cookies</q-item-label>
            </q-item-section>
          </q-item>

          <!-- FAQ -->
          <q-item clickable v-ripple to="/legal/faq" active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="help" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>FAQ</q-item-label>
              <q-item-label caption>Frequently asked questions</q-item-label>
            </q-item-section>
          </q-item>

          <!-- Cookie Preferences -->
          <q-item clickable v-ripple to="/legal/cookie-preferences" active-class="q-router-link-active">
            <q-item-section avatar>
              <q-icon name="cookie" color="primary" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Cookie Preferences</q-item-label>
              <q-item-label caption>Manage cookie settings</q-item-label>
            </q-item-section>
          </q-item>

        </q-list>
      </q-scroll-area>
    </q-drawer>

    <!-- Header -->
    <AppHeader
      :left-drawer-open="leftDrawerOpen"
      @toggle-drawer="leftDrawerOpen = !leftDrawerOpen"
    />

    <!-- No sidebar for landing page -->

    <!-- Main Content -->
    <q-page-container>
      <router-view v-scroll-to-top></router-view>
    </q-page-container>

    <!-- Footer -->
    <TheFooter />

    <!-- AI Chat Assistant -->
    <AIChatAssistant />

    <!-- News Ticker Component -->
    <NewsTickerComponent />

    <!-- Unified Authentication Dialogs -->
    <UnifiedAuthDialogs />
  </q-layout>
</template>

<script lang="ts">
export default {
  name: 'MainLayout'
};
</script>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthStore } from '../stores/auth';
import { useNotificationStore } from '../stores/notifications';
import { useMegaMenuStore } from '../stores/megaMenu';
// No need for Quasar loading import
import TheFooter from '../components/common/AppFooter.vue';
import AppHeader from '../components/common/AppHeader.vue';
import { default as UnifiedIcon } from '../components/ui/UnifiedIcon.vue';
import { default as AuthOptions } from '../components/auth/AuthOptions.vue';
import AIChatAssistant from '../components/ai/AIChatAssistant.vue';
import NewsTickerComponent from '../components/news/NewsTickerComponent.vue';
import UnifiedAuthDialogs from '../components/auth/UnifiedAuthDialogs.vue';
import { triggerSignIn, triggerSignUp } from '../services/unifiedAuthService';

const router = useRouter();
const route = useRoute();
const authStore = useAuthStore();
const notificationStore = useNotificationStore();
const megaMenuStore = useMegaMenuStore();




// Drawer control
const leftDrawerOpen = ref(false);

// Check if user is authenticated
const isAuthenticated = computed(() => authStore.isAuthenticated);

// Dialog controls (removed - now using unified auth service)

// Navigation function for drawer items
async function navigateTo(path: string) {
  // Close the drawer
  leftDrawerOpen.value = false;

  try {
    await router.push(path);
  } catch (error) {
    console.error('MainLayout: Navigation failed for path:', path, error);
  }
}



// Form data and validation rules (removed - now using unified auth service)



// Old authentication functions removed - now using unified auth service
</script>

<style scoped>
.container {
  width: 100%;
  max-width: 1400px;
}

.bg-menu {
  background-color: #dfefe6;
}





.signup-btn, .signin-btn {
  color: white;
  transition: all 0.3s ease;
  white-space: nowrap;
  padding: 8px 16px;
  border-radius: 0;
  flex: 1 1 50% !important;
  min-width: 0 !important;
  width: 50% !important;
  letter-spacing: 0.5px;
}

.dashboard-btn {
  color: #0D8A3E;
  transition: all 0.3s ease;
  white-space: nowrap;
  padding: 8px 16px;
  border-radius: 24px;
  border: 1px solid #0D8A3E;
}

.signup-btn:hover, .signin-btn:hover {
  opacity: 0.9;
}

.dashboard-btn:hover {
  background-color: rgba(13, 138, 62, 0.1);
}

.q-btn-group {
  overflow: hidden;
  display: flex;
  width: 100%;
}

/* Dialog styles */
.q-dialog .auth-dialog {
  border-radius: 12px;
  max-width: 400px;
}

.q-dialog .q-card__section {
  padding: 20px;
}

.q-dialog .text-h6 {
  color: #0D8A3E;
  font-weight: 600;
}

/* Drawer styles */
.q-drawer {
  background-color: #f5f5f5;
}

.q-item.q-router-link-active {
  color: #0D8A3E;
  background: rgba(13, 138, 62, 0.1);
  font-weight: 600;
  border-left: 3px solid #0D8A3E;
}

.q-item {
  color: #424242;
}

.q-item:hover {
  color: #0D8A3E;
  background: rgba(13, 138, 62, 0.05);
}

.q-item-label {
  color: #666666;
  font-weight: 500;
}

@media (max-width: 599px) {
  .container {
    padding: 0 16px !important;
  }

  .col-1 {
    display: none;
  }

  .col-10 {
    flex-basis: 100%;
    max-width: 100%;
  }

  .logo-text {
    font-size: 0.9rem;
  }

  .logo-image {
    height: 40px !important;
  }

  .col-6 {
    justify-content: flex-start !important;
    flex: 0 1 auto;
  }

  .col-3.flex.justify-end {
    margin-left: auto;
    flex: 0 0 auto;
  }

  .signup-btn, .signin-btn {
    font-size: 0.8rem;
    padding: 6px 12px !important;
    flex: 1 1 50% !important;
    width: 50% !important;
    min-width: 0 !important;
    max-width: 50% !important;
    height: auto !important;
    min-height: 36px !important;
    border-radius: 0 !important;
    margin: 0;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .auth-btn-group {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin: 0 4px;
    border-radius: 20px !important;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .auth-btn-group .q-btn {
    flex: 1 1 50% !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 50% !important;
    box-shadow: none;
  }

  /* First button in group (Sign Up) */
  .auth-btn-group .q-btn:first-child {
    border-top-left-radius: 20px !important;
    border-bottom-left-radius: 20px !important;
  }

  /* Last button in group (Sign In) */
  .auth-btn-group .q-btn:last-child {
    border-top-right-radius: 20px !important;
    border-bottom-right-radius: 20px !important;
  }

  .dashboard-btn {
    font-size: 0.8rem;
    padding: 4px 12px;
  }

  /* Dialog styles for mobile */
  .q-dialog .q-card {
    width: 90%;
  }




}

/* Mobile Navigation Drawer Styles */
.mobile-nav-drawer {
  background-color: white;
  box-shadow: 1px 0 10px rgba(0, 0, 0, 0.1);
}

.mobile-nav-drawer .q-item {
  min-height: 48px;
  border-radius: 8px;
  margin-bottom: 4px;
}

.mobile-nav-drawer .q-item:hover {
  background-color: rgba(13, 138, 62, 0.05);
}

.mobile-nav-drawer .q-item.q-router-link-active {
  background-color: rgba(13, 138, 62, 0.1);
  color: #0D8A3E;
  font-weight: 500;
}

.mobile-nav-drawer .q-expansion-item__content {
  padding-left: 8px;
}

.mobile-nav-drawer .q-item-label.header {
  padding: 16px 16px 8px 16px;
  font-size: 1rem;
  letter-spacing: 0.5px;
}

.mobile-nav-drawer .q-expansion-item.q-expansion-item--expanded {
  background-color: rgba(13, 138, 62, 0.03);
  border-radius: 8px;
}

.mobile-nav-drawer .q-icon {
  font-size: 20px;
}

/* Navigation Bar Styles */
.nav-link {
  text-decoration: none;
  color: #0D8A3E;
}



/* Navigation and Mega Menu Styles */
.nav-btn {
  position: relative;
}

.nav-btn::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background-color: #0D8A3E;
  transition: width 0.3s ease;
}

.nav-btn:hover::after {
  width: 80%;
}

/* Active menu indicator */
.nav-btn.active::after,
.nav-btn.router-link-active::after {
  width: 80%;
}

/* Active link styles */
.router-link-active {
  font-weight: 600;
  color: #0D8A3E !important;
}

/* Mega Menu Container */
.mega-menu-container {
  position: relative;
}

/* Mega Menu Dropdown */
.mega-menu-dropdown {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  margin-top: 5px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
  max-width: 600px;
  min-width: 500px;
}

@media (max-width: 600px) {
  .mega-menu-dropdown {
    min-width: 95vw;
    max-width: 95vw;
    left: 0;
    transform: none;
  }
}

.featured-card {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.featured-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.hours-card {
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background-color: rgba(13, 138, 62, 0.05);
}

.hours-card:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

/* Badge styles */
.badge-new {
  font-size: 10px;
  font-weight: bold;
  padding: 2px 6px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
