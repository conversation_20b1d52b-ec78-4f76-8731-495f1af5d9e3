// Supabase Edge Function for sending emails using Resend with SmileFactory domain
import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Credentials': 'true',
  'Access-Control-Max-Age': '86400'
}

// Environment variables - Use EMAIL_API_KEY for SmileFactory domain
const RESEND_API_KEY = Deno.env.get('EMAIL_API_KEY')
const DEFAULT_FROM_EMAIL = '<EMAIL>'
const DEFAULT_FROM_NAME = 'SmileFactory'
const SITE_URL = 'https://zbinnovation.co.zw'

// Function to extract name from email
function extractNameFromEmail(email: string): string | undefined {
  if (!email) return undefined

  const localPart = email.split('@')[0]
  const cleanedPart = localPart
    .replace(/^(mr|mrs|ms|dr|prof)\.?/i, '')
    .replace(/\.(jr|sr|ii|iii|iv)$/i, '')
    .replace(/[0-9]+/g, '')
    .replace(/[._-]/g, ' ')
    .trim()

  const name = cleanedPart
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ')

  return name.length > 1 ? name : undefined
}

// Function to strip HTML tags for plain text version
function stripHtml(html: string): string {
  return html
    .replace(/<[^>]*>/g, '')
    .replace(/\s+/g, ' ')
    .trim()
}

// Function to send email via Resend
async function sendEmail(emailData: any): Promise<any> {
  if (!RESEND_API_KEY) {
    console.error('❌ Resend API key (EMAIL_API_KEY) is not configured')
    throw new Error('Resend API key is not configured')
  }

  console.log('📧 Sending email with Resend (SmileFactory):', {
    to: emailData.to,
    subject: emailData.subject,
    from: `${DEFAULT_FROM_NAME} <${DEFAULT_FROM_EMAIL}>`,
    apiKeyPrefix: RESEND_API_KEY.substring(0, 10)
  })

  const payload = {
    from: `${DEFAULT_FROM_NAME} <${DEFAULT_FROM_EMAIL}>`,
    to: [emailData.to],
    subject: emailData.subject,
    html: emailData.html,
    text: emailData.text || stripHtml(emailData.html)
  }

  const response = await fetch('https://api.resend.com/emails', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${RESEND_API_KEY}`
    },
    body: JSON.stringify(payload)
  })

  if (!response.ok) {
    const errorText = await response.text()
    console.error('❌ Resend API error:', response.status, errorText)
    throw new Error(`Failed to send email: ${response.status} ${errorText}`)
  }

  console.log('✅ Email sent successfully!')
  return await response.json()
}

// Function to generate welcome email HTML
function generateWelcomeEmail(email: string, firstName?: string): { html: string; subject: string } {
  const extractedName = !firstName ? extractNameFromEmail(email) : undefined
  const greeting = firstName 
    ? `Hi ${firstName},` 
    : extractedName 
      ? `Hi ${extractedName},` 
      : 'Hi there,'

  const subject = 'Welcome to SmileFactory!'

  const html = `
    <div style="max-width: 600px; margin: 0 auto; padding: 24px; font-family: Arial, sans-serif;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://zbinnovation.co.zw/smile-factory-logo.svg" alt="SmileFactory" style="max-width: 200px;">
      </div>

      <h1 style="color: #0D8A3E; margin-bottom: 16px;">${greeting}</h1>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        Welcome to SmileFactory! We're excited to have you join our vibrant community of innovators,
        entrepreneurs, investors, mentors, and industry experts.
      </p>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        SmileFactory is Zimbabwe's premier innovation platform that connects brilliant minds and 
        transforms ideas into reality. Whether you're an innovator with the next big idea, an investor 
        looking for opportunities, or a mentor ready to guide the next generation, you're in the right place.
      </p>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        To get started and unlock all the amazing features of our platform, please complete your profile:
      </p>

      <div style="text-align: center; margin: 32px 0;">
        <a href="${SITE_URL}/dashboard" 
           style="background-color: #0D8A3E; color: white; padding: 12px 24px; text-decoration: none;
                  border-radius: 6px; display: inline-block; font-weight: bold;">
          Complete Your Profile
        </a>
      </div>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        Once your profile is complete, you'll be able to:
      </p>

      <ul style="margin-bottom: 16px; line-height: 1.5;">
        <li>🚀 <strong>Connect</strong> with other innovators, entrepreneurs, and mentors</li>
        <li>🎆 <strong>Discover</strong> exciting events, workshops, and networking opportunities</li>
        <li>💼 <strong>Explore</strong> our marketplace for innovative products and services</li>
        <li>🌐 <strong>Participate</strong> in our vibrant virtual community discussions</li>
        <li>✨ <strong>Get matched</strong> with relevant opportunities and collaborations</li>
        <li>💰 <strong>Access</strong> funding opportunities and investor connections</li>
      </ul>

      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 24px 0; border-left: 4px solid #0D8A3E;">
        <h3 style="color: #0D8A3E; margin: 0 0 12px 0; font-size: 18px;">🎆 Ready to Make Your Mark?</h3>
        <p style="margin: 0; line-height: 1.5; color: #333;">
          Join thousands of innovators who are already building the future of Zimbabwe's economy. 
          Your next breakthrough is just a connection away!
        </p>
      </div>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        If you have any questions or need assistance getting started, our friendly support team is here to help.
        Feel free to reach out to us at <a href="mailto:<EMAIL>" style="color: #0D8A3E;"><EMAIL></a>.
      </p>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        Thank you for joining SmileFactory – where innovation meets opportunity and dreams become reality!
      </p>

      <p style="margin-bottom: 8px; line-height: 1.5;">
        Best regards,
      </p>

      <p style="line-height: 1.5; font-weight: bold;">
        The SmileFactory Team 🚀
      </p>

      <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
        <p style="text-align: center;">
          © ${new Date().getFullYear()} SmileFactory. All rights reserved.<br>
          This email was sent to ${email}
        </p>
        <p style="text-align: center; margin-top: 12px;">
          <a href="${SITE_URL}" style="color: #0D8A3E; text-decoration: none;">Visit SmileFactory</a> | 
          <a href="${SITE_URL}/virtual-community" style="color: #0D8A3E; text-decoration: none;">Join Community</a> | 
          <a href="mailto:<EMAIL>" style="color: #0D8A3E; text-decoration: none;">Get Support</a>
        </p>
      </div>
    </div>
  `

  return { html, subject }
}

// Function to generate password reset email HTML
function generatePasswordResetEmail(email: string, resetLink: string, firstName?: string): { html: string; subject: string } {
  const extractedName = !firstName ? extractNameFromEmail(email) : undefined
  const greeting = firstName 
    ? `Hi ${firstName},` 
    : extractedName 
      ? `Hi ${extractedName},` 
      : 'Hi there,'

  const subject = 'Reset Your SmileFactory Password'

  const html = `
    <div style="max-width: 600px; margin: 0 auto; padding: 24px; font-family: Arial, sans-serif;">
      <div style="text-align: center; margin-bottom: 24px;">
        <img src="https://zbinnovation.co.zw/smile-factory-logo.svg" alt="SmileFactory" style="max-width: 200px;">
      </div>

      <h1 style="color: #0D8A3E; margin-bottom: 16px;">${greeting}</h1>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        We received a request to reset your password for your SmileFactory account.
        If you didn't make this request, you can safely ignore this email.
      </p>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        To reset your password, click the button below:
      </p>

      <div style="text-align: center; margin: 32px 0;">
        <a href="${resetLink}"
           style="background-color: #0D8A3E; color: white; padding: 12px 24px; text-decoration: none;
                  border-radius: 6px; display: inline-block; font-weight: bold;">
          Reset Password
        </a>
      </div>

      <p style="margin-bottom: 16px; line-height: 1.5;">
        This link will expire in 24 hours. If you need assistance, please contact our support team
        at <a href="mailto:<EMAIL>" style="color: #0D8A3E;"><EMAIL></a>.
      </p>

      <p style="margin-bottom: 8px; line-height: 1.5;">
        Best regards,
      </p>

      <p style="line-height: 1.5;">
        The SmileFactory Team
      </p>

      <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">
        <p style="text-align: center;">
          © ${new Date().getFullYear()} SmileFactory. All rights reserved.<br>
          This email was sent to ${email}
        </p>
      </div>
    </div>
  `

  return { html, subject }
}

// Main handler function
serve(async (req) => {
  // Handle CORS for preflight requests
  if (req.method === 'OPTIONS') {
    console.log('Handling OPTIONS request with CORS headers')
    return new Response('ok', {
      headers: {
        ...corsHeaders,
        'Content-Type': 'text/plain'
      }
    })
  }

  try {
    console.log('🚀 Received email request:', {
      method: req.method,
      url: req.url,
      timestamp: new Date().toISOString()
    })

    // Parse request body
    const requestData = await req.json()
    console.log('📋 Request data:', JSON.stringify(requestData))

    // Validate request
    if (!requestData.type || !requestData.data || !requestData.data.to) {
      console.error('❌ Invalid request - missing required fields:', JSON.stringify(requestData))
      return new Response(JSON.stringify({
        error: 'Invalid request. Missing required fields.'
      }), {
        status: 400,
        headers: {
          ...corsHeaders,
          'Content-Type': 'application/json'
        }
      })
    }

    // Process based on email type
    let emailContent: { html: string; subject: string }
    switch (requestData.type) {
      case 'welcome':
        emailContent = generateWelcomeEmail(requestData.data.to, requestData.data.firstName)
        break
      case 'password_reset':
        if (!requestData.data.customHtml) {
          return new Response(JSON.stringify({
            error: 'Password reset requires a reset link.'
          }), {
            status: 400,
            headers: {
              ...corsHeaders,
              'Content-Type': 'application/json'
            }
          })
        }
        emailContent = generatePasswordResetEmail(requestData.data.to, requestData.data.customHtml, requestData.data.firstName)
        break
      case 'custom':
        if (!requestData.data.customHtml || !requestData.data.subject) {
          return new Response(JSON.stringify({
            error: 'Custom email requires HTML content and subject.'
          }), {
            status: 400,
            headers: {
              ...corsHeaders,
              'Content-Type': 'application/json'
            }
          })
        }
        emailContent = {
          html: requestData.data.customHtml,
          subject: requestData.data.subject
        }
        break
      default:
        return new Response(JSON.stringify({
          error: `Unknown email type: ${requestData.type}`
        }), {
          status: 400,
          headers: {
            ...corsHeaders,
            'Content-Type': 'application/json'
          }
        })
    }

    // Send the email
    await sendEmail({
      to: requestData.data.to,
      subject: emailContent.subject,
      html: emailContent.html,
      text: requestData.data.customText || stripHtml(emailContent.html)
    })

    // Return success response
    return new Response(JSON.stringify({
      success: true,
      message: `Email sent to ${requestData.data.to}`
    }), {
      status: 200,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    })
  } catch (error) {
    // Handle errors
    console.error('❌ Error sending email:', error)
    return new Response(JSON.stringify({
      success: false,
      error: error.message || 'Failed to send email',
      stack: error.stack
    }), {
      status: 500,
      headers: {
        ...corsHeaders,
        'Content-Type': 'application/json'
      }
    })
  }
})
