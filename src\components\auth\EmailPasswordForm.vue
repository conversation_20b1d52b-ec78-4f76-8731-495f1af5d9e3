<template>
  <div class="email-password-form">
    <q-form @submit="handleSubmit" class="q-gutter-md">
      <q-input
        :model-value="formData.email"
        @update:model-value="(val) => updateForm('email', val)"
        type="email"
        label="Email"
        :rules="emailRules"
        outlined
        dense
        class="form-input"
      />
      
      <q-input
        :model-value="formData.password"
        @update:model-value="(val) => updateForm('password', val)"
        :type="isPwd ? 'password' : 'text'"
        label="Password"
        :rules="passwordRules"
        outlined
        dense
        class="form-input"
      >
        <template v-slot:append>
          <q-icon
            :name="isPwd ? 'visibility_off' : 'visibility'"
            class="cursor-pointer"
            @click="togglePasswordVisibility"
          />
        </template>
      </q-input>

      <!-- Terms and Conditions Acceptance -->
      <div class="q-mt-md">
        <q-checkbox
          :model-value="formData.acceptTerms"
          @update:model-value="(val) => updateForm('acceptTerms', val)"
          color="primary"
          class="terms-checkbox"
        >
          <span class="terms-text">
            I agree to the
            <router-link to="/legal/terms-conditions" target="_blank" class="terms-link">
              Terms and Conditions
            </router-link>
            and
            <router-link to="/legal/privacy-policy" target="_blank" class="terms-link">
              Privacy Policy
            </router-link>
          </span>
        </q-checkbox>
      </div>

      <!-- Category Context (if provided) -->
      <div v-if="selectedCategory" class="category-context">
        <div class="category-context__content">
          <unified-icon :name="selectedCategory.icon" size="1.5rem" color="primary" />
          <span class="category-context__text">
            Creating account as <strong>{{ selectedCategory.label }}</strong>
          </span>
        </div>
      </div>

      <div class="text-center q-mt-md">
        <q-btn
          type="submit"
          label="Create Account"
          :loading="loading"
          :disable="!formData.acceptTerms"
          class="zb-btn-primary full-width create-account-btn"
        />
      </div>
    </q-form>

    <!-- Back to method selection (if in multi-step flow) -->
    <div v-if="showBackButton" class="text-center q-mt-md">
      <q-btn
        flat
        no-caps
        color="grey-6"
        @click="handleBack"
        class="back-btn"
      >
        <unified-icon name="arrow_back" size="1rem" />
        Back to Sign-Up Methods
      </q-btn>
    </div>

    <!-- Toggle to Sign In (if not in multi-step flow) -->
    <div v-if="!showBackButton" class="text-center q-mt-md">
      <q-btn
        flat
        no-caps
        color="primary"
        @click="handleSwitchToSignIn"
        class="text-caption toggle-btn"
      >
        Already have an account? Sign In
      </q-btn>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import UnifiedIcon from '@/components/ui/UnifiedIcon.vue'
import { useUnifiedAuth } from '@/services/unifiedAuthService'
import type { CategoryOption } from '@/services/categoryService'

interface Props {
  selectedCategory?: CategoryOption | null
  showBackButton?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  selectedCategory: null,
  showBackButton: false
})

const emit = defineEmits<{
  'submit': []
  'back': []
  'switch-to-sign-in': []
}>()

const {
  state,
  signUpFormData,
  updateSignUpForm,
  togglePasswordVisibility,
  handleSignUp,
  switchToSignInForm,
  emailRules,
  passwordRules
} = useUnifiedAuth()

// Computed properties
const formData = computed(() => signUpFormData.value)
const loading = computed(() => state.value.loading)
const isPwd = computed(() => state.value.isPwd)

// Methods
const updateForm = (field: string, value: string | boolean) => {
  updateSignUpForm(field as any, value)
}

const handleSubmit = async () => {
  try {
    await handleSignUp()
    emit('submit')
  } catch (error) {
    console.error('Form submission error:', error)
  }
}

const handleBack = () => {
  emit('back')
}

const handleSwitchToSignIn = () => {
  switchToSignInForm()
  emit('switch-to-sign-in')
}
</script>

<style scoped>
.email-password-form {
  max-width: 400px;
  margin: 0 auto;
  padding: 1rem;
}

.form-input {
  margin-bottom: 1rem;
}

.terms-checkbox {
  margin-bottom: 1rem;
}

.terms-text {
  font-size: 0.875rem;
  line-height: 1.4;
}

.terms-link {
  color: #0D8A3E;
  text-decoration: none;
  font-weight: 500;
}

.terms-link:hover {
  text-decoration: underline;
}

.category-context {
  background: rgba(13, 138, 62, 0.1);
  border: 1px solid rgba(13, 138, 62, 0.2);
  border-radius: 8px;
  padding: 1rem;
  margin: 1rem 0;
}

.category-context__content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  justify-content: center;
}

.category-context__text {
  font-size: 0.9rem;
  color: #0D8A3E;
}

.create-account-btn {
  min-height: 44px;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.create-account-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(13, 138, 62, 0.3);
}

.back-btn {
  color: #666;
  font-size: 0.875rem;
}

.toggle-btn {
  font-size: 0.875rem;
}

/* Responsive design */
@media (max-width: 480px) {
  .email-password-form {
    padding: 0.5rem;
  }
  
  .category-context {
    padding: 0.75rem;
  }
  
  .category-context__content {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .terms-link {
    color: #0D8A3E;
    text-decoration: underline;
  }
  
  .category-context {
    background: rgba(13, 138, 62, 0.2);
    border-color: #0D8A3E;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .terms-text {
    color: #ccc;
  }
  
  .category-context {
    background: rgba(13, 138, 62, 0.15);
    border-color: rgba(13, 138, 62, 0.3);
  }
  
  .category-context__text {
    color: #4ade80;
  }
  
  .back-btn {
    color: #999;
  }
}
</style>
