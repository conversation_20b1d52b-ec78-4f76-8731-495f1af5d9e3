<template>
  <section class="ecosystem-map-section">
    <div class="background-overlay"></div>
    <div class="container q-mx-auto q-px-md">
      <div class="row">
        <div class="col-1" />
        <div class="col-10">
          <div class="text-center">
            <div class="section-header">
              <h2 class="section-title">Our Innovation Ecosystem</h2>
              <p class="section-description">
                Discover how our platform connects all stakeholders in the innovation ecosystem, creating a powerful network of collaboration and growth.
              </p>
            </div>
          </div>

          <div class="ecosystem-grid">
            <div
              v-for="(stakeholder, index) in stakeholders"
              :key="index"
              class="ecosystem-card"
              :style="{ animationDelay: `${index * 0.1}s` }"
              @click="navigateToProfiles(stakeholder.type)"
            >
              <div class="card-content">
                <div class="icon-container">
                  <q-icon :name="stakeholder.icon" size="2.5rem" color="white" />
                </div>
                <h3 class="card-title">{{ stakeholder.title }}</h3>
                <p class="card-description">{{ stakeholder.description }}</p>
                <div class="card-stats">
                  <span class="stat-number">{{ stakeholder.count }}+</span>
                  <span class="stat-label">Active Members</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-1" />
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const stakeholders = ref([
  {
    icon: 'rocket_launch',
    title: 'Startups',
    description: 'Innovative companies building the future',
    count: 150,
    type: 'startups'
  },
  {
    icon: 'account_balance',
    title: 'Investors',
    description: 'Funding partners seeking opportunities',
    count: 75,
    type: 'investors'
  },
  {
    icon: 'school',
    title: 'Mentors',
    description: 'Experienced guides sharing knowledge',
    count: 200,
    type: 'mentors'
  },
  {
    icon: 'engineering',
    title: 'Industry Experts',
    description: 'Specialists providing technical insights',
    count: 120,
    type: 'experts'
  },
  {
    icon: 'account_balance_wallet',
    title: 'Academic Institutions',
    description: 'Universities fostering research',
    count: 45,
    type: 'academic'
  },
  {
    icon: 'business',
    title: 'Government',
    description: 'Policy makers supporting innovation',
    count: 30,
    type: 'government'
  }
])

const navigateToProfiles = (type: string) => {
  router.push(`/innovation-community?filter=${type}`)
}
</script>

<style scoped>
.ecosystem-map-section {
  position: relative;
  padding: 80px 0;
  background-image: url('@/assets/hero/group-afro-americans-working-together (1).jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  overflow: hidden;
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(13, 138, 62, 0.85) 0%, 
    rgba(131, 186, 38, 0.75) 50%,
    rgba(13, 138, 62, 0.85) 100%);
  z-index: 1;
}

.container {
  position: relative;
  z-index: 2;
}

.section-header {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 60px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 20px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.section-description {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.ecosystem-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 40px;
}

.ecosystem-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: translateY(30px);
  animation: slideUp 0.6s ease forwards;
}

.ecosystem-card:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
}

.card-content {
  text-align: center;
  color: white;
}

.icon-container {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  backdrop-filter: blur(10px);
}

.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: white;
}

.card-description {
  font-size: 1rem;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 20px;
  line-height: 1.5;
}

.card-stats {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #83BA26;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 1px;
}

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .ecosystem-map-section {
    padding: 60px 0;
    background-attachment: scroll;
  }

  .section-header {
    padding: 30px 20px;
    margin-bottom: 40px;
  }

  .section-title {
    font-size: 2rem;
  }

  .section-description {
    font-size: 1rem;
  }

  .ecosystem-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .ecosystem-card {
    padding: 25px;
  }

  .card-title {
    font-size: 1.3rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .section-header {
    padding: 20px 15px;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .ecosystem-card {
    padding: 20px;
  }
}
</style>
