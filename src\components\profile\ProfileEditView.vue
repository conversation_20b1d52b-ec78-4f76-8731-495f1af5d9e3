<template>
  <div class="profile-edit-view">
    <div v-if="loading" class="q-pa-md flex flex-center">
      <q-spinner color="primary" size="3em" />
      <div class="q-ml-md">Loading profile data...</div>
    </div>

    <div v-else-if="error" class="q-pa-md flex flex-center column">
      <q-card class="q-pa-lg text-center" style="max-width: 500px">
        <q-card-section>
          <unified-icon name="error" color="warning" size="4em" />
          <div class="text-h5 q-mt-md">Error Loading Profile</div>
          <div class="text-subtitle1 q-mt-sm">{{ error }}</div>
        </q-card-section>
        <q-card-actions align="center" class="q-gutter-md">
          <q-btn
            color="light-green-8"
            label="Back to Dashboard"
            to="/dashboard"
            class="q-mt-md"
            icon-right="arrow_back"
          />
        </q-card-actions>
      </q-card>
    </div>

    <div v-else>
      <!-- Edit Header -->
      <q-card class="q-mb-md">
        <q-card-section class="bg-primary text-white">
          <div class="row items-center">
            <div class="col">
              <div class="text-h6">Edit Profile</div>
              <div class="text-subtitle2">{{ formatProfileType(profile?.profile_type) }}</div>
            </div>
            <div class="col-auto">
              <q-btn
                flat
                round
                color="white"
                @click="cancel"
              >
                <unified-icon name="visibility" color="white" />
                <q-tooltip>Cancel and View Profile</q-tooltip>
              </q-btn>
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Profile Form -->
      <q-form @submit="saveProfile" class="q-gutter-md">
        <!-- Personal Details Section -->
        <q-card class="q-mb-md">
          <q-card-section class="bg-primary text-white">
            <div class="text-h6">
              <unified-icon name="person" class="q-mr-sm" />
              Personal Details
            </div>
          </q-card-section>

          <q-card-section>
            <div class="row q-col-gutter-md">
              <div class="col-12 col-md-6">
                <q-input
                  v-model="formData.first_name"
                  label="First Name"
                  outlined
                  :rules="[val => !!val || 'First name is required']"
                />
              </div>

              <div class="col-12 col-md-6">
                <q-input
                  v-model="formData.last_name"
                  label="Last Name"
                  outlined
                  :rules="[val => !!val || 'Last name is required']"
                />
              </div>

              <div class="col-12 col-md-6">
                <q-input
                  v-model="formData.email"
                  label="Email"
                  outlined
                  type="email"
                  readonly
                />
              </div>

              <div class="col-12 col-md-6">
                <q-input
                  v-model="formData.phone"
                  label="Phone"
                  outlined
                />
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- Profile Details Section -->
        <q-card class="q-mb-md">
          <q-card-section class="bg-primary text-white">
            <div class="text-h6">
              <unified-icon name="badge" class="q-mr-sm" />
              Profile Details
            </div>
          </q-card-section>

          <q-card-section>
            <div class="row q-col-gutter-md">


              <div class="col-12 col-md-6">
                <q-toggle
                  v-model="formData.is_public"
                  label="Public Profile"
                  color="primary"
                />
              </div>

              <div class="col-12 col-md-6">
                <q-input
                  v-model="formData.website"
                  label="Website"
                  outlined
                  type="url"
                />
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- Social Media Section -->
        <q-card class="q-mb-md">
          <q-card-section class="bg-primary text-white">
            <div class="text-h6">
              <unified-icon name="share" class="q-mr-sm" />
              Social Media
            </div>
          </q-card-section>

          <q-card-section>
            <div class="row q-col-gutter-md">
              <div class="col-12 col-md-6">
                <q-input
                  v-model="formData.linkedin"
                  label="LinkedIn"
                  outlined
                />
              </div>

              <div class="col-12 col-md-6">
                <q-input
                  v-model="formData.twitter"
                  label="Twitter"
                  outlined
                />
              </div>

              <div class="col-12 col-md-6">
                <q-input
                  v-model="formData.facebook"
                  label="Facebook"
                  outlined
                />
              </div>

              <div class="col-12 col-md-6">
                <q-input
                  v-model="formData.instagram"
                  label="Instagram"
                  outlined
                />
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- Contact Information Section -->
        <q-card class="q-mb-md">
          <q-card-section class="bg-primary text-white">
            <div class="text-h6">
              <unified-icon name="contact_mail" class="q-mr-sm" />
              Contact Information
            </div>
          </q-card-section>

          <q-card-section>
            <div class="row q-col-gutter-md">
              <div class="col-12 col-md-6">
                <q-input
                  v-model="formData.contact_email"
                  label="Contact Email"
                  outlined
                  type="email"
                />
              </div>

              <div class="col-12 col-md-6">
                <q-input
                  v-model="formData.contact_phone"
                  label="Contact Phone"
                  outlined
                />
              </div>

              <div class="col-12 col-md-6">
                <q-input
                  v-model="formData.whatsapp"
                  label="WhatsApp"
                  outlined
                />
              </div>

              <div class="col-12 col-md-6">
                <q-select
                  v-model="formData.preferred_contact_method"
                  :options="contactMethodOptions"
                  label="Preferred Contact Method"
                  outlined
                />
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- Location Section -->
        <q-card class="q-mb-md">
          <q-card-section class="bg-primary text-white">
            <div class="text-h6">
              <unified-icon name="location_on" class="q-mr-sm" />
              Location
            </div>
          </q-card-section>

          <q-card-section>
            <div class="row q-col-gutter-md">
              <div class="col-12">
                <q-input
                  v-model="formData.address"
                  label="Address"
                  outlined
                />
              </div>

              <div class="col-12 col-md-6">
                <q-input
                  v-model="formData.city"
                  label="City"
                  outlined
                />
              </div>

              <div class="col-12 col-md-6">
                <q-input
                  v-model="formData.state_province"
                  label="State/Province"
                  outlined
                />
              </div>

              <div class="col-12 col-md-6">
                <q-input
                  v-model="formData.country"
                  label="Country"
                  outlined
                />
              </div>

              <div class="col-12 col-md-6">
                <q-input
                  v-model="formData.postal_code"
                  label="Postal Code"
                  outlined
                />
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- Form Actions -->
        <div class="row q-col-gutter-md justify-center q-my-lg">
          <div class="col-auto">
            <q-btn
              label="Cancel"
              color="grey"
              @click="cancel"
              icon-right="cancel"
            />
          </div>
          <div class="col-auto">
            <q-btn
              label="Save Profile"
              color="primary"
              type="submit"
              icon-right="save"
              :loading="saving"
            />
          </div>
        </div>
      </q-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '../../stores/auth'
import { useProfileStore } from '../../stores/profile'
import { useNotificationStore } from '../../stores/notifications'
import UnifiedIcon from '../ui/UnifiedIcon.vue'

const props = defineProps({
  profileId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['saved', 'cancel'])

// State
const loading = ref(true)
const saving = ref(false)
const error = ref('')
const profile = ref(null)
const formData = ref({
  first_name: '',
  last_name: '',
  email: '',
  phone: '',
  is_public: false,
  website: '',
  linkedin: '',
  twitter: '',
  facebook: '',
  instagram: '',
  contact_email: '',
  contact_phone: '',
  whatsapp: '',
  preferred_contact_method: '',
  address: '',
  city: '',
  state_province: '',
  country: '',
  postal_code: ''
})

// Stores
const authStore = useAuthStore()
const profileStore = useProfileStore()
const notifications = useNotificationStore()

// Options for select inputs
const contactMethodOptions = [
  'Email',
  'Phone',
  'WhatsApp',
  'Telegram',
  'LinkedIn',
  'Other'
]

// Methods
function formatProfileType(type) {
  if (!type) return 'Unknown'
  
  // Convert snake_case to Title Case
  return type
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}

function cancel() {
  emit('cancel')
}

async function loadProfile() {
  loading.value = true
  error.value = ''
  
  try {
    console.log('ProfileEditView: Loading profile for ID:', props.profileId)

    // Initialize the profile store if needed
    if (profileStore.userProfiles.length === 0) {
      console.log('ProfileEditView: Initializing profile store')
      await profileStore.initialize()
    }

    // Use the fetchProfile method from the profile store
    const profileData = await profileStore.fetchProfile(props.profileId)

    if (!profileData) {
      console.error('ProfileEditView: Profile not found in store')
      error.value = 'Profile not found'
      profile.value = null
      return
    }

    console.log('ProfileEditView: Profile loaded from store:', profileData)
    profile.value = profileData

    // Initialize form data with profile data
    formData.value = {
      first_name: profileData.first_name || '',
      last_name: profileData.last_name || '',
      email: profileData.email || '',
      phone: profileData.phone || '',
      profile_name: profileData.profile_name || '',
      is_public: profileData.is_public || false,
      bio: profileData.bio || '',
      website: profileData.website || '',
      linkedin: profileData.linkedin || '',
      twitter: profileData.twitter || '',
      facebook: profileData.facebook || '',
      instagram: profileData.instagram || '',
      contact_email: profileData.contact_email || '',
      contact_phone: profileData.contact_phone || '',
      whatsapp: profileData.whatsapp || '',
      preferred_contact_method: profileData.preferred_contact_method || '',
      address: profileData.address || '',
      city: profileData.city || '',
      state_province: profileData.state_province || '',
      country: profileData.country || '',
      postal_code: profileData.postal_code || ''
    }
  } catch (err) {
    console.error('ProfileEditView: Error loading profile:', err)
    error.value = err.message || 'Failed to load profile'
  } finally {
    loading.value = false
  }
}

async function saveProfile() {
  saving.value = true
  
  try {
    console.log('ProfileEditView: Saving profile data:', formData.value)
    
    // Update the profile in the store
    await profileStore.updateProfile({
      ...profile.value,
      ...formData.value
    })
    
    notifications.success('Profile updated successfully')
    emit('saved')
  } catch (err) {
    console.error('ProfileEditView: Error saving profile:', err)
    notifications.error(err.message || 'Failed to save profile')
  } finally {
    saving.value = false
  }
}

// Lifecycle hooks
onMounted(async () => {
  await loadProfile()
})
</script>

<style scoped>
.profile-edit-view {
  max-width: 1200px;
  margin: 0 auto;
}
</style>
