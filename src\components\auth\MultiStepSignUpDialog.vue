<template>
  <full-screen-dialog
    :model-value="isOpen"
    @update:model-value="updateModelValue"
    @close="handleClose"
    :title="dialogTitle"
    :subtitle="dialogSubtitle"
  >
    <!-- Category Selection Step -->
    <category-selection-step
      v-if="currentStep === 'category'"
      @category-selected="handleCategorySelected"
      @continue="handleCategoryConfirmed"
      @back="handleClose"
    />

    <!-- Sign-In Method Selection Step -->
    <sign-in-method-selection-step
      v-else-if="currentStep === 'method'"
      :selected-category-id="selectedCategoryId"
      @method-selected="handleMethodSelected"
      @change-category="handleChangeCategory"
      @back="handleBackToCategory"
    />

    <!-- Registration Form Step -->
    <div v-else-if="currentStep === 'form'" class="registration-form-step">
      <div class="form-container">
        <div class="form-header">
          <h2 class="form-title">Create Your Account</h2>
          <p class="form-subtitle">
            Complete your registration as a <strong>{{ selectedCategoryLabel }}</strong>
          </p>
        </div>

        <email-password-form
          :selected-category="state.selectedCategory"
          :show-back-button="true"
          @submit="handleFormSubmit"
          @back="handleBackToMethod"
        />
      </div>
    </div>
  </full-screen-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import FullScreenDialog from '@/components/ui/FullScreenDialog.vue'
import CategorySelectionStep from './CategorySelectionStep.vue'
import SignInMethodSelectionStep from './SignInMethodSelectionStep.vue'
import EmailPasswordForm from './EmailPasswordForm.vue'
import { useUnifiedAuth } from '@/services/unifiedAuthService'
import { useCategoryService, type CategoryOption } from '@/services/categoryService'

const { state, selectCategory, proceedToMethodSelection, proceedToRegistrationForm, backToCategorySelection, backToMethodSelection, closeAllDialogs } = useUnifiedAuth()
const { getCategoryById } = useCategoryService()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'close': []
}>()

// Computed properties
const isOpen = computed(() => state.value.isMultiStepSignUpOpen)
const currentStep = computed(() => state.value.signUpStep)
const selectedCategoryId = computed(() => state.value.selectedCategory?.id || null)

const selectedCategoryLabel = computed(() => {
  if (selectedCategoryId.value) {
    const category = getCategoryById(selectedCategoryId.value)
    return category?.label || 'Unknown Category'
  }
  return ''
})

const dialogTitle = computed(() => {
  switch (currentStep.value) {
    case 'category':
      return 'Welcome to the Platform'
    case 'method':
      return 'Choose Sign-Up Method'
    case 'form':
      return 'Create Account'
    default:
      return 'Sign Up'
  }
})

const dialogSubtitle = computed(() => {
  switch (currentStep.value) {
    case 'category':
      return 'Let\'s get you connected with the right community'
    case 'method':
      return 'Select your preferred registration method'
    case 'form':
      return 'Just a few more details to get started'
    default:
      return ''
  }
})

// Methods
const updateModelValue = (value: boolean) => {
  emit('update:modelValue', value)
}

const handleClose = () => {
  closeAllDialogs()
  emit('close')
}

const handleCategorySelected = (category: CategoryOption) => {
  selectCategory(category)
}

const handleCategoryConfirmed = (categoryId: string) => {
  proceedToMethodSelection()
}

const handleMethodSelected = (method: string) => {
  if (method === 'email-password') {
    proceedToRegistrationForm()
  }
  // Handle other methods when they become available
}

const handleChangeCategory = () => {
  backToCategorySelection()
}

const handleBackToCategory = () => {
  backToCategorySelection()
}

const handleFormSubmit = () => {
  // Form submission is handled by the EmailPasswordForm component
  // The form will trigger the post-registration onboarding automatically
  console.log('Form submitted successfully')
}

const handleBackToMethod = () => {
  backToMethodSelection()
}
</script>

<style scoped>
.registration-form-step {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 100vh;
  padding: 2rem 0;
}

.form-container {
  max-width: 600px;
  margin: 0 auto;
  width: 100%;
}

.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.form-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.form-subtitle {
  font-size: 1.25rem;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

.form-placeholder {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  padding: 3rem 2rem;
  text-align: center;
  color: #666;
}

/* Responsive design */
@media (max-width: 768px) {
  .form-title {
    font-size: 2rem;
  }
  
  .form-subtitle {
    font-size: 1.1rem;
  }
  
  .form-placeholder {
    padding: 2rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .registration-form-step {
    padding: 1.5rem 0;
  }
  
  .form-title {
    font-size: 1.75rem;
  }
  
  .form-subtitle {
    font-size: 1rem;
  }
  
  .form-placeholder {
    padding: 1.5rem 1rem;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .form-title {
    color: #000;
  }
  
  .form-subtitle {
    color: #333;
  }
  
  .form-placeholder {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(0, 0, 0, 0.3);
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .form-title {
    color: #f0f0f0;
  }
  
  .form-subtitle {
    color: #ccc;
  }
  
  .form-placeholder {
    background: rgba(30, 30, 30, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
    color: #ccc;
  }
}
</style>
