<template>
  <div
    class="category-card glass-card glass-card--interactive"
    :class="{
      'glass-card--selected': isSelected,
      'glass-fade-in': animate
    }"
    @click="handleClick"
    @keydown.enter="handleClick"
    @keydown.space.prevent="handleClick"
    tabindex="0"
    role="button"
    :aria-pressed="isSelected"
    :aria-label="`Select ${category.label} category: ${category.description}`"
  >
    <div class="category-card__content">
      <!-- Icon with dynamic color -->
      <div class="category-card__icon" :style="{ backgroundColor: iconBackgroundColor }">
        <unified-icon
          :name="category.icon"
          size="2.5rem"
          :color="isSelected ? '#FFFFFF' : iconColor"
        />
      </div>

      <!-- Title -->
      <div class="category-card__title">
        {{ category.label }}
      </div>

      <!-- Description -->
      <div class="category-card__description">
        {{ category.description }}
      </div>

      <!-- Selection indicator -->
      <div v-if="isSelected" class="category-card__indicator">
        <unified-icon 
          name="check_circle" 
          size="1.5rem"
          color="primary"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import UnifiedIcon from './UnifiedIcon.vue'

// Define color palette for different categories
const categoryColors = {
  innovator: { icon: '#FF6B6B', bg: 'rgba(255, 107, 107, 0.1)' },
  mentor: { icon: '#4ECDC4', bg: 'rgba(78, 205, 196, 0.1)' },
  investor: { icon: '#45B7D1', bg: 'rgba(69, 183, 209, 0.1)' },
  industry_expert: { icon: '#96CEB4', bg: 'rgba(150, 206, 180, 0.1)' },
  academic: { icon: '#FFEAA7', bg: 'rgba(255, 234, 167, 0.1)' },
  student: { icon: '#DDA0DD', bg: 'rgba(221, 160, 221, 0.1)' },
  academic_institution: { icon: '#98D8C8', bg: 'rgba(152, 216, 200, 0.1)' },
  government_organisation: { icon: '#F7DC6F', bg: 'rgba(247, 220, 111, 0.1)' }
}
import type { CategoryOption } from '@/services/categoryService'

interface Props {
  category: CategoryOption
  selected?: boolean
  animate?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  animate: true
})

const emit = defineEmits<{
  'select': [category: CategoryOption]
}>()

const isSelected = computed(() => props.selected)

// Get dynamic colors based on category
const iconColor = computed(() => {
  const colors = categoryColors[props.category.id as keyof typeof categoryColors]
  return colors?.icon || '#0D8A3E'
})

const iconBackgroundColor = computed(() => {
  if (isSelected.value) {
    return '#0D8A3E'
  }
  const colors = categoryColors[props.category.id as keyof typeof categoryColors]
  return colors?.bg || 'rgba(13, 138, 62, 0.1)'
})

const handleClick = () => {
  emit('select', props.category)
}
</script>

<style scoped>
.category-card {
  position: relative;
  padding: 2rem 1.5rem;
  text-align: center;
  min-height: 180px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
}

.category-card__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  width: 100%;
  height: 100%;
  position: relative;
}

.category-card__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.category-card:hover .category-card__icon {
  transform: scale(1.1);
}

.category-card__title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.category-card__description {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
  text-align: center;
  max-width: 200px;
}

.category-card__indicator {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
  background: white;
  border-radius: 50%;
  padding: 0.25rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Selected state styling */
.glass-card--selected {
  background: var(--glass-primary-hover);
  border-color: var(--glass-primary-border);
  box-shadow: 0 0 0 2px rgba(13, 138, 62, 0.3), var(--glass-shadow-hover);
}

.glass-card--selected .category-card__title {
  color: #0D8A3E;
  font-weight: 700;
}

.glass-card--selected .category-card__description {
  color: #0D8A3E;
  opacity: 0.8;
}

/* Hover effects */
.category-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--glass-shadow-hover);
}

.category-card:active {
  transform: translateY(-2px) scale(0.98);
}

/* Focus styles for accessibility */
.category-card:focus {
  outline: 2px solid #0D8A3E;
  outline-offset: 2px;
}

/* Animation delays for staggered entrance */
.category-card:nth-child(1) { animation-delay: 0.1s; }
.category-card:nth-child(2) { animation-delay: 0.2s; }
.category-card:nth-child(3) { animation-delay: 0.3s; }
.category-card:nth-child(4) { animation-delay: 0.4s; }
.category-card:nth-child(5) { animation-delay: 0.5s; }
.category-card:nth-child(6) { animation-delay: 0.6s; }
.category-card:nth-child(7) { animation-delay: 0.7s; }
.category-card:nth-child(8) { animation-delay: 0.8s; }

/* Responsive design */
@media (max-width: 768px) {
  .category-card {
    padding: 1.5rem 1rem;
    min-height: 160px;
  }
  
  .category-card__content {
    gap: 0.75rem;
  }
  
  .category-card__icon {
    margin-bottom: 0.25rem;
  }
  
  .category-card__title {
    font-size: 1.1rem;
  }
  
  .category-card__description {
    font-size: 0.85rem;
    max-width: 180px;
  }
}

@media (max-width: 480px) {
  .category-card {
    padding: 1rem;
    min-height: 140px;
  }
  
  .category-card__content {
    gap: 0.5rem;
  }
  
  .category-card__title {
    font-size: 1rem;
  }
  
  .category-card__description {
    font-size: 0.8rem;
    max-width: 160px;
  }
  
  .category-card__indicator {
    top: -0.25rem;
    right: -0.25rem;
    padding: 0.125rem;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .category-card__title {
    color: #000;
  }
  
  .category-card__description {
    color: #333;
  }
  
  .glass-card--selected .category-card__title,
  .glass-card--selected .category-card__description {
    color: #0D8A3E;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .category-card,
  .category-card__icon {
    transition: none;
  }
  
  .category-card:hover {
    transform: none;
  }
  
  .category-card:active {
    transform: none;
  }
  
  .glass-fade-in {
    animation: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .category-card__title {
    color: #f0f0f0;
  }
  
  .category-card__description {
    color: #ccc;
  }
  
  .category-card__indicator {
    background: #2a2a2a;
  }
}
</style>
