<template>
  <section class="features-section q-py-xl">
    <div class="container q-mx-auto q-px-md">
      <div class="row">
        <div class="col-1" />
        <div class="col-10">
          <div class="text-center">
            <h2 class="text-h3 text-weight-light q-mb-md">Platform Features</h2>
            <p class="text-body1 q-mb-xl text-center">
              Explore the powerful tools and features that make our innovation ecosystem unique and effective.
            </p>
          </div>
          
          <div class="features-grid">
            <div
              v-for="(feature, index) in features"
              :key="index"
              class="feature-card scroll-item"
              :style="{ animationDelay: `${index * 0.1}s` }"
            >
              <div class="feature-icon-container">
                <q-icon :name="feature.icon" size="3rem" color="primary" class="feature-icon" />
                <div class="icon-bg"></div>
              </div>
              <h3 class="text-h6 q-mt-md q-mb-sm">{{ feature.title }}</h3>
              <p class="text-body2">{{ feature.description }}</p>
            </div>
          </div>
          
          <div class="text-center q-mt-xl">
            <q-btn
              color="primary"
              label="Explore All Features"
              no-caps
              class="q-px-xl"
              unelevated
              rounded
              @click="scrollToSignup"
            />
          </div>
        </div>
        <div class="col-1" />
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const features = ref([
  {
    icon: 'people',
    title: 'Community Networking',
    description: 'Connect with entrepreneurs, investors, mentors, and industry experts in a vibrant community.'
  },
  {
    icon: 'school',
    title: 'Learning Resources',
    description: 'Access a wealth of educational content and training opportunities to grow your skills.'
  },
  {
    icon: 'trending_up',
    title: 'Growth Analytics',
    description: 'Track your progress and measure success with comprehensive analytics and insights.'
  },
  {
    icon: 'handshake',
    title: 'Partnership Matching',
    description: 'Find the perfect partners, collaborators, and team members for your projects.'
  },
  {
    icon: 'event',
    title: 'Events & Workshops',
    description: 'Participate in exclusive events, workshops, and networking opportunities.'
  },
  {
    icon: 'support',
    title: '24/7 Support',
    description: 'Get help when you need it with our dedicated support team and community.'
  }
])

const scrollToSignup = () => {
  const signupSection = document.getElementById('signup-section')
  if (signupSection) {
    signupSection.scrollIntoView({ behavior: 'smooth' })
  }
}

// Initialize scroll animations
onMounted(() => {
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  }

  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate')
      }
    })
  }, observerOptions)

  // Observe all scroll items
  const scrollItems = document.querySelectorAll('.scroll-item')
  scrollItems.forEach((item) => {
    observer.observe(item)
  })
})
</script>

<style scoped>
.features-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 80px 0;
  position: relative;
}

.container {
  width: 100%;
  max-width: 1400px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
  margin-top: 40px;
}

.feature-card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  text-align: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(30px);
}

.feature-card.animate {
  opacity: 1;
  transform: translateY(0);
}

.feature-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
}

.feature-icon-container {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  margin: 0 auto;
}

.icon-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(13, 138, 62, 0.1), rgba(131, 186, 38, 0.1));
  border-radius: 50%;
  z-index: 1;
}

.feature-icon {
  position: relative;
  z-index: 2;
}

.feature-card h3 {
  color: #0D8A3E;
  font-weight: 600;
  margin-bottom: 16px;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .features-section {
    padding: 60px 0;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .feature-card {
    padding: 24px;
    /* Ensure visibility on mobile */
    opacity: 1 !important;
    transform: translateY(0) !important;
  }

  .feature-icon-container {
    width: 60px;
    height: 60px;
  }

  .feature-icon {
    font-size: 2rem !important;
  }
}

@media (max-width: 480px) {
  .feature-card {
    padding: 20px;
  }

  .features-grid {
    gap: 20px;
  }
}
</style>
