<template>
  <section class="ecosystem-map-section q-py-xl">
    <div class="container q-mx-auto q-px-md">
      <div class="row">
        <div class="col-1" />
        <div class="col-10">
          <div class="text-center">
            <h2 class="text-h3 text-weight-light q-mb-md">Our Innovation Ecosystem</h2>
            <p class="text-body1 q-mb-xl text-center">
              Discover how our platform connects all stakeholders in the innovation ecosystem, creating a powerful network of collaboration and growth.
            </p>
          </div>

          <!-- Ecosystem Cards Grid -->
          <div class="ecosystem-cards-grid">
            <div
              v-for="(stakeholder, index) in stakeholders"
              :key="index"
              class="ecosystem-card"
              :style="{ animationDelay: `${index * 0.1}s` }"
              @click="navigateToProfiles(stakeholder.type)"
              role="button"
              :aria-label="`View ${stakeholder.title} profiles`"
            >
              <div class="card-image-container">
                <img :src="stakeholder.image" :alt="stakeholder.title" class="card-image" />
                <div class="card-overlay">
                  <q-icon :name="stakeholder.icon" size="2rem" color="white" />
                </div>
              </div>
              <div class="card-content">
                <h3 class="card-title">{{ stakeholder.title }}</h3>
                <p class="card-description">{{ stakeholder.description }}</p>
                <div class="card-stats">
                  <span class="stat-number">{{ stakeholder.count }}+</span>
                  <span class="stat-label">Active Members</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-1" />
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const stakeholders = ref([
  {
    icon: 'rocket_launch',
    title: 'Startups',
    description: 'Innovative companies building the future',
    count: 150,
    type: 'startups',
    image: '/src/assets/hero/group-afro-americans-working-together (1).jpg'
  },
  {
    icon: 'account_balance',
    title: 'Investors',
    description: 'Funding partners seeking opportunities',
    count: 75,
    type: 'investors',
    image: '/src/assets/hero/businessman-businesswoman-bumping-their-fist-front-corporate-building.jpg'
  },
  {
    icon: 'psychology',
    title: 'Mentors',
    description: 'Experienced guides sharing knowledge',
    count: 200,
    type: 'mentors',
    image: '/src/assets/hero/business-concept-business-people-shaking-hands-showing-mutual-agreement-betweent-their-companies-firms.jpg'
  },
  {
    icon: 'school',
    title: 'Academia',
    description: 'Universities fostering research',
    count: 45,
    type: 'academia',
    image: '/src/assets/hero/family-enjoying-their-quality-winter-time.jpg'
  },
  {
    icon: 'domain',
    title: 'Corporates',
    description: 'Established companies driving innovation',
    count: 80,
    type: 'corporates',
    image: '/src/assets/hero/group-friends-standing-outside-clothing-store-city.jpg'
  },
  {
    icon: 'account_balance',
    title: 'Government',
    description: 'Policy makers supporting innovation',
    count: 30,
    type: 'government',
    image: '/src/assets/hero/landing.jpg'
  }
])

const navigateToProfiles = (type: string) => {
  router.push(`/innovation-community?filter=${type}`)
}
</script>

<style scoped>
.ecosystem-map-section {
  background-color: white;
  padding: 80px 0;
  position: relative;
  overflow: hidden;
}

.ecosystem-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 40px;
}

.ecosystem-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: translateY(30px);
  animation: slideUp 0.6s ease forwards;
}

.ecosystem-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.card-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.ecosystem-card:hover .card-image {
  transform: scale(1.05);
}

.card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(13, 138, 62, 0.8), rgba(131, 186, 38, 0.8));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.ecosystem-card:hover .card-overlay {
  opacity: 1;
}

.card-content {
  padding: 24px;
  text-align: center;
}

.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #0D8A3E;
  margin-bottom: 12px;
}

.card-description {
  font-size: 1rem;
  color: #666;
  margin-bottom: 20px;
  line-height: 1.5;
}

.card-stats {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #83BA26;
}

.stat-label {
  font-size: 0.9rem;
  color: #888;
  text-transform: uppercase;
  letter-spacing: 1px;
}

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .ecosystem-map-section {
    padding: 60px 0;
  }

  .ecosystem-cards-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .ecosystem-card {
    /* Ensure visibility on mobile */
    opacity: 1 !important;
    transform: translateY(0) !important;
    animation: none;
  }

  .card-image-container {
    height: 160px;
  }

  .card-content {
    padding: 20px;
  }

  .card-title {
    font-size: 1.3rem;
  }

  .stat-number {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .ecosystem-cards-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .card-content {
    padding: 16px;
  }

  .card-image-container {
    height: 140px;
  }
}
</style>
