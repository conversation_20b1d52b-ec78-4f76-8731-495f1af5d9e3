<template>
  <div class="category-selection-grid">
    <!-- Category Grid -->
    <div class="glass-container">
      <div class="category-grid">
        <category-card
          v-for="(category, index) in categories"
          :key="category.id"
          :category="category"
          :selected="selectedCategoryId === category.id"
          :animate="true"
          @select="handleCategorySelect"
          :style="{ animationDelay: `${index * 0.1}s` }"
          class="category-grid-item"
        />
      </div>
    </div>

    <!-- Brief explanation for selected category -->
    <div v-if="selectedCategory" class="selected-category-info glass-card glass-card--primary">
      <div class="info-header">
        <unified-icon :name="selectedCategory.icon" size="1.5rem" color="primary" />
        <h4>{{ selectedCategory.label }}</h4>
      </div>
      <p class="info-description">{{ getDetailedDescription(selectedCategory.id) }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import CategoryCard from '@/components/ui/CategoryCard.vue'
import UnifiedIcon from '@/components/ui/UnifiedIcon.vue'
import { useCategoryService, type CategoryOption } from '@/services/categoryService'

interface Props {
  selectedCategoryId?: string | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'category-selected': [category: CategoryOption]
}>()

const { getCategories, getCategoryById } = useCategoryService()

const categories = getCategories()

const selectedCategory = computed(() => {
  return props.selectedCategoryId ? getCategoryById(props.selectedCategoryId) : null
})

const handleCategorySelect = (category: CategoryOption) => {
  emit('category-selected', category)
}

// Enhanced descriptions for each category
const getDetailedDescription = (categoryId: string): string => {
  const descriptions = {
    innovator: 'Perfect for entrepreneurs, startup founders, and creative minds with groundbreaking ideas. Connect with mentors, investors, and fellow innovators to bring your vision to life.',
    mentor: 'Ideal for experienced professionals who want to guide the next generation. Share your expertise, build meaningful relationships, and help shape the future of innovation.',
    investor: 'Designed for angel investors, VCs, and funding organizations. Discover promising startups, evaluate investment opportunities, and build a diverse portfolio.',
    industry_expert: 'Great for specialists with deep domain knowledge. Share insights, consult on projects, and establish yourself as a thought leader in your field.',
    academic: 'Perfect for researchers, professors, and academic professionals. Collaborate on research, share knowledge, and bridge the gap between academia and industry.',
    student: 'Ideal for students seeking opportunities, mentorship, and real-world experience. Connect with professionals, find internships, and accelerate your career.',
    academic_institution: 'Designed for universities, colleges, and research institutions. Showcase programs, connect with industry partners, and promote academic excellence.',
    government_organisation: 'Perfect for government agencies and public sector organizations. Foster innovation, support economic development, and create public-private partnerships.'
  }
  
  return descriptions[categoryId as keyof typeof descriptions] || 'Join our community and connect with like-minded professionals.'
}
</script>

<style scoped>
.category-selection-grid {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  width: 100%;
}

.glass-container {
  background: var(--glass-bg-light);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: var(--glass-shadow);
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  width: 100%;
}

.category-grid-item {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.selected-category-info {
  padding: 1.5rem;
  text-align: center;
  border-radius: 16px;
  margin-top: 1rem;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.info-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.info-header h4 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #0D8A3E;
}

.info-description {
  margin: 0;
  font-size: 0.95rem;
  line-height: 1.6;
  color: #555;
  max-width: 600px;
  margin: 0 auto;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .glass-container {
    padding: 1.5rem;
  }
  
  .category-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 1rem;
  }
  
  .selected-category-info {
    padding: 1rem;
  }
  
  .info-header h4 {
    font-size: 1.125rem;
  }
  
  .info-description {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .category-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
  
  .glass-container {
    padding: 1rem;
  }
}
</style>
