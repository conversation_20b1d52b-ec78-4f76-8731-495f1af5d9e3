<template>
  <q-dialog v-model="isOpen" persistent>
    <q-card class="auth-dialog compact-signup-dialog" style="min-width: 450px; max-width: 500px">
      <!-- Header -->
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">Join Our Platform</div>
        <q-space />
        <q-btn flat round dense @click="handleClose">
          <q-icon name="close" />
        </q-btn>
      </q-card-section>

      <!-- Main Content -->
      <q-card-section class="q-pt-sm">
        <!-- Step 1: Category Selection (if not selected) -->
        <div v-if="!selectedCategory" class="category-selection-section">
          <div class="section-title">Choose your category</div>
          <div class="section-subtitle">Connect with the right community</div>
          
          <div class="compact-category-grid">
            <div
              v-for="category in categories"
              :key="category.id"
              class="compact-category-card"
              @click="handleCategorySelect(category)"
              @keydown.enter="handleCategorySelect(category)"
              @keydown.space.prevent="handleCategorySelect(category)"
              tabindex="0"
              role="button"
              :aria-label="`Select ${category.label} category`"
            >
              <unified-icon :name="category.icon" size="1.5rem" color="primary" />
              <span class="category-label">{{ category.label }}</span>
            </div>
          </div>
        </div>

        <!-- Step 2: Registration Form (if category selected) -->
        <div v-else class="registration-section">
          <!-- Selected Category Display -->
          <div class="selected-category-display">
            <unified-icon :name="selectedCategory.icon" size="1.25rem" color="primary" />
            <span class="selected-category-text">{{ selectedCategory.label }}</span>
            <q-btn
              flat
              dense
              round
              size="sm"
              icon="edit"
              color="primary"
              @click="handleChangeCategory"
              class="change-category-btn"
              aria-label="Change category"
            />
          </div>

          <!-- Registration Form -->
          <div class="form-section">
            <div class="section-title">Create your account</div>
            
            <q-form @submit="handleSubmit" class="q-gutter-sm">
              <q-input
                :model-value="formData.email"
                @update:model-value="updateEmail"
                type="email"
                label="Email"
                :rules="emailRules"
                outlined
                dense
                class="form-input"
              />

              <q-input
                :model-value="formData.password"
                @update:model-value="updatePassword"
                :type="isPwd ? 'password' : 'text'"
                label="Password"
                :rules="passwordRules"
                outlined
                dense
                class="form-input"
              >
                <template v-slot:append>
                  <q-icon
                    :name="isPwd ? 'visibility_off' : 'visibility'"
                    class="cursor-pointer"
                    @click="togglePasswordVisibility"
                  />
                </template>
              </q-input>

              <!-- Terms Acceptance -->
              <div class="terms-section">
                <q-checkbox
                  :model-value="formData.acceptTerms"
                  @update:model-value="updateAcceptTerms"
                  color="primary"
                  dense
                  class="terms-checkbox"
                >
                  <span class="terms-text">
                    I agree to the
                    <router-link to="/legal/terms-conditions" target="_blank" class="terms-link">
                      Terms
                    </router-link>
                    and
                    <router-link to="/legal/privacy-policy" target="_blank" class="terms-link">
                      Privacy Policy
                    </router-link>
                  </span>
                </q-checkbox>
              </div>

              <!-- Submit Button -->
              <q-btn
                type="submit"
                label="Create Account"
                :loading="loading"
                :disable="!formData.acceptTerms || !selectedCategory"
                class="zb-btn-primary full-width q-mt-md"
                no-caps
              />
            </q-form>
          </div>
        </div>
      </q-card-section>

      <!-- Footer -->
      <q-card-section class="text-center q-pt-none">
        <q-btn
          flat
          no-caps
          color="primary"
          @click="handleSwitchToSignIn"
          class="text-caption toggle-btn"
          size="sm"
        >
          Already have an account? Sign In
        </q-btn>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import UnifiedIcon from '@/components/ui/UnifiedIcon.vue'
import { useUnifiedAuth } from '@/services/unifiedAuthService'
import { useCategoryService, type CategoryOption } from '@/services/categoryService'

const {
  state,
  signUpFormData,
  updateSignUpForm,
  togglePasswordVisibility,
  handleSignUp,
  switchToSignInForm,
  closeAllDialogs,
  emailRules,
  passwordRules
} = useUnifiedAuth()

const { getCategories, saveSelectedCategory, getSelectedCategory, clearSelectedCategory } = useCategoryService()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'close': []
}>()

// Local state
const selectedCategory = ref<CategoryOption | null>(null)

// Computed properties
const isOpen = computed(() => state.value.isMultiStepSignUpOpen)
const categories = computed(() => getCategories())
const formData = computed(() => signUpFormData.value)
const loading = computed(() => state.value.loading)
const isPwd = computed(() => state.value.isPwd)

// Methods
const handleClose = () => {
  clearSelectedCategory()
  selectedCategory.value = null
  closeAllDialogs()
  emit('close')
}

const handleCategorySelect = (category: CategoryOption) => {
  selectedCategory.value = category
  saveSelectedCategory(category.id)
  // Update the form data with the selected category
  updateSignUpForm('selectedCategory', category)
}

const handleChangeCategory = () => {
  selectedCategory.value = null
  clearSelectedCategory()
  updateSignUpForm('selectedCategory', null)
}

const handleSubmit = async () => {
  if (!selectedCategory.value) {
    console.error('No category selected')
    return
  }

  try {
    // Ensure the selected category is in the form data
    updateSignUpForm('selectedCategory', selectedCategory.value)
    await handleSignUp()
    // Success handling is done in the auth service
  } catch (error) {
    console.error('Registration error:', error)
  }
}

const handleSwitchToSignIn = () => {
  switchToSignInForm()
  emit('close')
}

// Form field update methods
const updateEmail = (value: string) => {
  updateSignUpForm('email', value)
}

const updatePassword = (value: string) => {
  updateSignUpForm('password', value)
}

const updateAcceptTerms = (value: boolean) => {
  updateSignUpForm('acceptTerms', value)
}

// Lifecycle
onMounted(() => {
  // Check if there's a previously selected category
  const savedCategory = getSelectedCategory()
  if (savedCategory) {
    selectedCategory.value = savedCategory
    updateSignUpForm('selectedCategory', savedCategory)
  }
})
</script>

<style scoped>
.compact-signup-dialog {
  border-radius: 12px;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 0.25rem;
}

.section-subtitle {
  font-size: 0.875rem;
  color: #666;
  margin-bottom: 1rem;
}

.compact-category-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.compact-category-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 0.75rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.compact-category-card:hover {
  border-color: #0D8A3E;
  background: rgba(13, 138, 62, 0.05);
  transform: translateY(-1px);
}

.compact-category-card:focus {
  outline: 2px solid #0D8A3E;
  outline-offset: 2px;
}

.category-label {
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

.selected-category-display {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background: rgba(13, 138, 62, 0.1);
  border: 1px solid rgba(13, 138, 62, 0.2);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.selected-category-text {
  flex: 1;
  font-size: 0.9rem;
  font-weight: 500;
  color: #0D8A3E;
}

.change-category-btn {
  min-width: auto;
  width: 24px;
  height: 24px;
}

.form-section {
  margin-top: 0.5rem;
}

.form-input {
  margin-bottom: 0.5rem;
}

.terms-section {
  margin: 0.75rem 0;
}

.terms-checkbox {
  align-items: flex-start;
}

.terms-text {
  font-size: 0.8rem;
  line-height: 1.3;
  margin-left: 0.25rem;
}

.terms-link {
  color: #0D8A3E;
  text-decoration: none;
  font-weight: 500;
}

.terms-link:hover {
  text-decoration: underline;
}

.toggle-btn {
  font-size: 0.8rem;
}

/* Mobile responsiveness */
@media (max-width: 480px) {
  .compact-signup-dialog {
    min-width: 320px;
    max-width: 95vw;
    margin: 1rem;
  }
  
  .compact-category-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .compact-category-card {
    padding: 0.75rem;
  }
  
  .category-label {
    font-size: 0.75rem;
  }
  
  .section-title {
    font-size: 1rem;
  }
  
  .section-subtitle {
    font-size: 0.8rem;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .compact-category-card {
    border-color: #333;
  }
  
  .compact-category-card:hover {
    border-color: #0D8A3E;
    background: rgba(13, 138, 62, 0.1);
  }
  
  .section-title {
    color: #000;
  }
  
  .section-subtitle {
    color: #333;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .section-title {
    color: #f0f0f0;
  }
  
  .section-subtitle {
    color: #ccc;
  }
  
  .compact-category-card {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .compact-category-card:hover {
    background: rgba(13, 138, 62, 0.1);
    border-color: #0D8A3E;
  }
  
  .selected-category-display {
    background: rgba(13, 138, 62, 0.15);
    border-color: rgba(13, 138, 62, 0.3);
  }
  
  .selected-category-text {
    color: #4ade80;
  }
  
  .terms-text {
    color: #ccc;
  }
}
</style>
