import { ref, onMounted, onUnmounted } from 'vue'

/**
 * Composable for handling scroll-based animations
 * Uses Intersection Observer API for performance
 */
export function useScrollAnimations() {
  const observer = ref<IntersectionObserver | null>(null)
  const animatedElements = ref<Set<Element>>(new Set())

  /**
   * Initialize scroll animations
   * Sets up intersection observer to trigger animations when elements come into view
   */
  const initializeScrollAnimations = () => {
    // Clean up existing observer
    if (observer.value) {
      observer.value.disconnect()
    }

    // Wait for DOM to be ready, then prepare animations
    setTimeout(() => {
      // Find all elements with scroll animation
      const scrollElements = document.querySelectorAll('.scroll-section')

      // Add the ready class to enable animations after a brief delay
      scrollElements.forEach((element) => {
        element.classList.add('scroll-animate-ready')
      })

      // Create new intersection observer
      observer.value = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting && !animatedElements.value.has(entry.target)) {
              // Add animation class
              entry.target.classList.add('animate')

              // Mark as animated to prevent re-animation
              animatedElements.value.add(entry.target)

              // Optional: Stop observing this element after animation
              observer.value?.unobserve(entry.target)
            }
          })
        },
        {
          // Trigger when 20% of the element is visible
          threshold: 0.2,
          // Start animation 100px before element enters viewport
          rootMargin: '0px 0px -100px 0px'
        }
      )

      // Observe each element
      scrollElements.forEach((element) => {
        if (observer.value) {
          observer.value.observe(element)
        }
      })
    }, 100) // Small delay to ensure DOM is ready
  }

  /**
   * Reset animations (useful for route changes)
   */
  const resetAnimations = () => {
    animatedElements.value.clear()

    // Remove animation classes from all scroll sections
    const scrollElements = document.querySelectorAll('.scroll-section')
    scrollElements.forEach((element) => {
      element.classList.remove('animate', 'scroll-animate-ready')
    })
  }

  /**
   * Cleanup function
   */
  const cleanup = () => {
    if (observer.value) {
      observer.value.disconnect()
      observer.value = null
    }
    animatedElements.value.clear()
  }

  // Auto cleanup on unmount
  onUnmounted(() => {
    cleanup()
  })

  return {
    initializeScrollAnimations,
    resetAnimations,
    cleanup
  }
}

/**
 * Utility function to add staggered animations to child elements
 * Useful for animating lists or grids with delay between items
 */
export function useStaggeredAnimations(containerSelector: string, itemSelector: string, delay: number = 100) {
  const animateStaggered = () => {
    const container = document.querySelector(containerSelector)
    if (!container) return

    const items = container.querySelectorAll(itemSelector)
    
    items.forEach((item, index) => {
      setTimeout(() => {
        item.classList.add('animate')
      }, index * delay)
    })
  }

  return {
    animateStaggered
  }
}

/**
 * Utility function for scroll-triggered counters
 * Animates numbers from 0 to target value when element comes into view
 */
export function useScrollCounter(targetValue: number, duration: number = 2000) {
  const currentValue = ref(0)
  const isAnimating = ref(false)

  const animateCounter = () => {
    if (isAnimating.value) return

    isAnimating.value = true
    const startTime = Date.now()
    const startValue = 0

    const animate = () => {
      const elapsed = Date.now() - startTime
      const progress = Math.min(elapsed / duration, 1)
      
      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4)
      
      currentValue.value = Math.round(startValue + (targetValue - startValue) * easeOutQuart)

      if (progress < 1) {
        requestAnimationFrame(animate)
      } else {
        isAnimating.value = false
      }
    }

    requestAnimationFrame(animate)
  }

  return {
    currentValue,
    isAnimating,
    animateCounter
  }
}
