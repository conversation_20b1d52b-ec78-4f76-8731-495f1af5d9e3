<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile Creation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #0D8A3E;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0a6b31;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>Profile Creation Enhancement Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Category Pre-selection</h2>
        <p>This test simulates the category pre-selection functionality.</p>
        
        <button class="test-button" onclick="testCategoryPreselection('innovator')">Test Innovator Category</button>
        <button class="test-button" onclick="testCategoryPreselection('mentor')">Test Mentor Category</button>
        <button class="test-button" onclick="testCategoryPreselection('investor')">Test Investor Category</button>
        <button class="test-button" onclick="clearCategory()">Clear Category</button>
        
        <div id="category-results"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Profile Type Mapping</h2>
        <p>This test verifies that categories map correctly to profile types.</p>
        
        <button class="test-button" onclick="testProfileTypeMapping()">Test Profile Type Mapping</button>
        
        <div id="mapping-results"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: LocalStorage Persistence</h2>
        <p>This test checks if category selection persists in localStorage.</p>
        
        <button class="test-button" onclick="testLocalStoragePersistence()">Test Persistence</button>
        
        <div id="persistence-results"></div>
    </div>

    <div class="test-section">
        <h2>Instructions for Manual Testing</h2>
        <div class="info">
            <h3>To test the profile creation enhancements:</h3>
            <ol>
                <li>Run the tests above to verify localStorage functionality</li>
                <li>Open the application in a new tab: <a href="http://localhost:9000" target="_blank">http://localhost:9000</a></li>
                <li>Sign up with a new account and select a category</li>
                <li>Complete the registration process</li>
                <li>Navigate to the profile creation page</li>
                <li>Verify that:
                    <ul>
                        <li>The category dropdown is pre-selected with your chosen category</li>
                        <li>A confirmation message shows "You selected [CATEGORY] profile"</li>
                        <li>The button text says "Proceed" instead of "Create Profile"</li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>

    <script>
        // Category options (matching the application)
        const categoryOptions = [
            { id: 'innovator', label: 'Innovator', profileType: 'innovator' },
            { id: 'mentor', label: 'Mentor', profileType: 'mentor' },
            { id: 'investor', label: 'Investor', profileType: 'investor' },
            { id: 'industry_expert', label: 'Industry Expert', profileType: 'industry_expert' },
            { id: 'professional', label: 'Professional', profileType: 'professional' },
            { id: 'academic_student', label: 'Academic Student', profileType: 'academic_student' },
            { id: 'academic_institution', label: 'Academic Institution', profileType: 'academic_institution' },
            { id: 'organisation', label: 'Organisation', profileType: 'organisation' }
        ];

        const STORAGE_KEY = 'zb-selected-category';

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `result ${type}`;
            result.textContent = message;
            container.appendChild(result);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        function testCategoryPreselection(categoryId) {
            clearResults('category-results');
            
            try {
                // Save category to localStorage
                localStorage.setItem(STORAGE_KEY, categoryId);
                
                // Verify it was saved
                const saved = localStorage.getItem(STORAGE_KEY);
                if (saved === categoryId) {
                    addResult('category-results', `✓ Category "${categoryId}" saved to localStorage`, 'success');
                    
                    // Find the category object
                    const category = categoryOptions.find(cat => cat.id === categoryId);
                    if (category) {
                        addResult('category-results', `✓ Category object found: ${category.label}`, 'success');
                        addResult('category-results', `✓ Maps to profile type: ${category.profileType}`, 'success');
                    } else {
                        addResult('category-results', `✗ Category object not found for ID: ${categoryId}`, 'error');
                    }
                } else {
                    addResult('category-results', `✗ Failed to save category to localStorage`, 'error');
                }
            } catch (error) {
                addResult('category-results', `✗ Error: ${error.message}`, 'error');
            }
        }

        function clearCategory() {
            clearResults('category-results');
            localStorage.removeItem(STORAGE_KEY);
            addResult('category-results', '✓ Category cleared from localStorage', 'success');
        }

        function testProfileTypeMapping() {
            clearResults('mapping-results');
            
            let allPassed = true;
            
            categoryOptions.forEach(category => {
                if (category.profileType) {
                    addResult('mapping-results', `✓ ${category.label} → ${category.profileType}`, 'success');
                } else {
                    addResult('mapping-results', `✗ ${category.label} has no profile type mapping`, 'error');
                    allPassed = false;
                }
            });
            
            if (allPassed) {
                addResult('mapping-results', '✓ All categories have valid profile type mappings', 'success');
            }
        }

        function testLocalStoragePersistence() {
            clearResults('persistence-results');
            
            try {
                // Test saving and retrieving
                const testCategory = 'innovator';
                localStorage.setItem(STORAGE_KEY, testCategory);
                
                const retrieved = localStorage.getItem(STORAGE_KEY);
                if (retrieved === testCategory) {
                    addResult('persistence-results', '✓ localStorage save/retrieve works', 'success');
                } else {
                    addResult('persistence-results', '✗ localStorage save/retrieve failed', 'error');
                    return;
                }
                
                // Test clearing
                localStorage.removeItem(STORAGE_KEY);
                const afterClear = localStorage.getItem(STORAGE_KEY);
                if (afterClear === null) {
                    addResult('persistence-results', '✓ localStorage clear works', 'success');
                } else {
                    addResult('persistence-results', '✗ localStorage clear failed', 'error');
                }
                
                addResult('persistence-results', '✓ All localStorage operations working correctly', 'success');
                
            } catch (error) {
                addResult('persistence-results', `✗ localStorage error: ${error.message}`, 'error');
            }
        }

        // Run initial tests
        window.onload = function() {
            addResult('category-results', 'Ready to test category pre-selection', 'info');
            addResult('mapping-results', 'Ready to test profile type mapping', 'info');
            addResult('persistence-results', 'Ready to test localStorage persistence', 'info');
        };
    </script>
</body>
</html>
