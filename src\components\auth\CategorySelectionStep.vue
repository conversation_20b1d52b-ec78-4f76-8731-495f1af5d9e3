<template>
  <div class="category-selection-step">
    <!-- Header Section -->
    <div class="step-header">
      <div class="step-header__content">
        <h1 class="step-title">
          Sign up to the platform to get connections
        </h1>
        <p class="step-subtitle">
          Choose your category to connect with the right people and opportunities
        </p>
      </div>
    </div>

    <!-- Category Grid -->
    <div class="step-body">
      <div class="glass-container">
        <div class="category-grid">
          <category-card
            v-for="(category, index) in categories"
            :key="category.id"
            :category="category"
            :selected="selectedCategoryId === category.id"
            :animate="true"
            @select="handleCategorySelect"
            :style="{ animationDelay: `${index * 0.1}s` }"
          />
        </div>
      </div>
    </div>

    <!-- Footer Section -->
    <div class="step-footer">
      <div class="step-footer__content">
        <div class="footer-info">
          <p class="footer-text">
            <unified-icon name="info" size="1rem" color="primary" />
            You can update your category later in your profile settings
          </p>
        </div>
        
        <div class="footer-actions">
          <q-btn
            flat
            no-caps
            color="grey-6"
            @click="handleBack"
            class="glass-btn"
          >
            <unified-icon name="arrow_back" size="1rem" />
            Back
          </q-btn>
          
          <q-btn
            unelevated
            no-caps
            color="primary"
            :disabled="!selectedCategoryId"
            @click="handleContinue"
            class="glass-btn glass-btn--primary continue-btn"
            :loading="loading"
          >
            Continue
            <unified-icon name="arrow_forward" size="1rem" />
          </q-btn>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import CategoryCard from '@/components/ui/CategoryCard.vue'
import UnifiedIcon from '@/components/ui/UnifiedIcon.vue'
import { useCategoryService, type CategoryOption } from '@/services/categoryService'

const emit = defineEmits<{
  'category-selected': [category: CategoryOption]
  'continue': [categoryId: string]
  'back': []
}>()

const { getCategories, saveSelectedCategory, getSelectedCategory } = useCategoryService()

// State
const selectedCategoryId = ref<string | null>(null)
const loading = ref(false)

// Computed
const categories = computed(() => getCategories())

// Methods
const handleCategorySelect = (category: CategoryOption) => {
  selectedCategoryId.value = category.id
  saveSelectedCategory(category.id)
  emit('category-selected', category)
}

const handleContinue = async () => {
  if (!selectedCategoryId.value) return
  
  loading.value = true
  try {
    // Small delay for better UX
    await new Promise(resolve => setTimeout(resolve, 300))
    emit('continue', selectedCategoryId.value)
  } finally {
    loading.value = false
  }
}

const handleBack = () => {
  emit('back')
}

// Lifecycle
onMounted(() => {
  // Check if there's a previously selected category
  const savedCategory = getSelectedCategory()
  if (savedCategory) {
    selectedCategoryId.value = savedCategory.id
  }
})
</script>

<style scoped>
.category-selection-step {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 100vh;
}

.step-header {
  flex-shrink: 0;
  padding: 2rem 0;
  text-align: center;
}

.step-header__content {
  max-width: 800px;
  margin: 0 auto;
}

.step-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.step-subtitle {
  font-size: 1.25rem;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

.step-body {
  flex: 1;
  padding: 1rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  padding: 1rem 0;
  max-width: 1200px;
  width: 100%;
}

.step-footer {
  flex-shrink: 0;
  padding: 2rem 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.step-footer__content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

.footer-info {
  flex: 1;
}

.footer-text {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  font-size: 0.9rem;
  color: #666;
}

.footer-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.continue-btn {
  min-width: 120px;
  padding: 0.75rem 2rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.continue-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.continue-btn:not(:disabled):hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(13, 138, 62, 0.3);
}

/* Responsive design */
@media (max-width: 1024px) {
  .category-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.25rem;
  }
}

@media (max-width: 768px) {
  .step-title {
    font-size: 2rem;
  }
  
  .step-subtitle {
    font-size: 1.1rem;
  }
  
  .category-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1rem;
  }
  
  .step-footer__content {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }
  
  .footer-actions {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .step-header {
    padding: 1.5rem 0;
  }
  
  .step-title {
    font-size: 1.75rem;
  }
  
  .step-subtitle {
    font-size: 1rem;
  }
  
  .category-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .step-footer {
    padding: 1.5rem 0;
  }
  
  .footer-actions {
    flex-direction: column;
    width: 100%;
    gap: 0.75rem;
  }
  
  .continue-btn {
    width: 100%;
  }
}

/* Animation for category cards */
.category-grid .category-card {
  opacity: 0;
  animation: slideInUp 0.6s ease-out forwards;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .category-grid .category-card {
    animation: none;
    opacity: 1;
  }
  
  .continue-btn:not(:disabled):hover {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .step-title {
    color: #000;
  }
  
  .step-subtitle,
  .footer-text {
    color: #333;
  }
  
  .step-footer {
    background: rgba(255, 255, 255, 1);
    border-top-color: #000;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .step-title {
    color: #f0f0f0;
  }
  
  .step-subtitle,
  .footer-text {
    color: #ccc;
  }
  
  .step-footer {
    background: rgba(30, 30, 30, 0.95);
    border-top-color: rgba(255, 255, 255, 0.1);
  }
}
</style>
