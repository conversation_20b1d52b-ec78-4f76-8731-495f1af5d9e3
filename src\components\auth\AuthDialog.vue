<template>
  <FullScreenDialog
    :model-value="modelValue"
    @update:model-value="updateModelValue"
    @close="handleClose"
    :title="title"
    :subtitle="subtitle"
    class="auth-dialog"
  >
    <template #header-right>
      <q-btn
        v-if="!persistent"
        flat
        round
        dense
        icon="close"
        color="white"
        @click="handleClose"
        class="close-button"
        aria-label="Close dialog"
      />
    </template>

    <!-- Main content with two sections -->
    <div class="auth-dialog-content">
      <!-- Top Section: Category Selection -->
      <div class="auth-section category-section" :class="{ 'section-disabled': !showCategorySection }">
        <div class="section-header">
          <h3 class="section-title">Choose Your Category</h3>
          <p class="section-subtitle">Select the category that best describes you to connect with the right people and opportunities</p>
        </div>
        
        <div class="section-content">
          <slot name="category-section" />
        </div>
      </div>

      <!-- Divider -->
      <div class="section-divider">
        <div class="divider-line"></div>
        <div class="divider-text">then</div>
        <div class="divider-line"></div>
      </div>

      <!-- Bottom Section: Sign-In Method -->
      <div class="auth-section signin-section" :class="{ 'section-disabled': !showSignInSection }">
        <div class="section-header">
          <h3 class="section-title">Choose Sign-Up Method</h3>
          <p class="section-subtitle">Select how you'd like to create your account</p>
        </div>
        
        <div class="section-content">
          <slot name="signin-section" />
        </div>
      </div>
    </div>

    <!-- Footer -->
    <template #footer>
      <slot name="footer" />
    </template>
  </FullScreenDialog>
</template>

<script setup lang="ts">
import FullScreenDialog from '@/components/ui/FullScreenDialog.vue'

interface Props {
  modelValue: boolean
  title?: string
  subtitle?: string
  persistent?: boolean
  showCategorySection?: boolean
  showSignInSection?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  title: 'Welcome to the Platform',
  subtitle: 'Join our community of innovators, mentors, and industry experts',
  persistent: false,
  showCategorySection: true,
  showSignInSection: false
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'close': []
}>()

const updateModelValue = (value: boolean) => {
  emit('update:modelValue', value)
}

const handleClose = () => {
  if (!props.persistent) {
    emit('close')
    emit('update:modelValue', false)
  }
}
</script>

<style scoped>
.auth-dialog {
  z-index: 9500;
}

.auth-dialog-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  height: 100%;
  padding: 1rem 0;
}

.auth-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.section-disabled {
  opacity: 0.5;
  pointer-events: none;
  filter: grayscale(50%);
}

.section-header {
  margin-bottom: 1.5rem;
  text-align: center;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #0D8A3E;
  margin-bottom: 0.5rem;
}

.section-subtitle {
  font-size: 1rem;
  color: #666;
  line-height: 1.5;
  max-width: 600px;
  margin: 0 auto;
}

.section-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.section-divider {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin: 1rem 0;
  opacity: 0.7;
}

.divider-line {
  flex: 1;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(13, 138, 62, 0.3) 50%,
    transparent 100%
  );
}

.divider-text {
  font-size: 0.875rem;
  color: #0D8A3E;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 0.5rem 1rem;
  background: rgba(13, 138, 62, 0.1);
  border-radius: 20px;
  border: 1px solid rgba(13, 138, 62, 0.2);
}

/* Category section specific styles */
.category-section {
  min-height: 40%;
}

/* Sign-in section specific styles */
.signin-section {
  min-height: 35%;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .auth-dialog-content {
    gap: 1.5rem;
    padding: 0.5rem 0;
  }
  
  .section-title {
    font-size: 1.25rem;
  }
  
  .section-subtitle {
    font-size: 0.9rem;
  }
  
  .section-header {
    margin-bottom: 1rem;
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: 1.125rem;
  }
  
  .section-subtitle {
    font-size: 0.85rem;
  }
  
  .divider-text {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }
}
</style>
