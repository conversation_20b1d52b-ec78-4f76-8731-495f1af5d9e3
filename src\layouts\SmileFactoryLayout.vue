<template>
  <q-layout view="hHh lpR lFf" class="smile-factory-layout">
    <!-- Main Content -->
    <q-page-container>
      <router-view v-scroll-to-top></router-view>
    </q-page-container>

    <!-- <PERSON> Assistant -->
    <AIChatAssistant />

    <!-- <PERSON><PERSON> -->
    <CookieConsentBanner />
  </q-layout>
</template>

<script lang="ts">
export default {
  name: 'SmileFactoryLayout'
};
</script>

<script setup lang="ts">
import AIChatAssistant from '../components/ai/AIChatAssistant.vue';
import CookieConsentBanner from '../components/legal/CookieConsentBanner.vue';
</script>

<style scoped>
.smile-factory-layout {
  /* Minimal layout styles for the dedicated Smile Factory landing page */
  min-height: 100vh;
}
</style>
