<template>
  <q-page class="q-pa-md">
    <!-- Full-page loading overlay -->
    <div v-if="loading" class="full-page-loader">
      <q-spinner-dots color="primary" size="80px" />
      <div class="q-mt-md text-subtitle1">{{ loadingMessage }}</div>
    </div>

    <!-- Main content - only show when not in loading state -->
    <div v-else class="row justify-center">
      <div class="col-12 col-md-8 col-lg-6">
        <q-card class="profile-creation-card">
          <q-card-section class="bg-primary text-white">
            <div class="text-h6">
              Create Your Profile
              <q-badge color="light-green-5" class="q-ml-sm">
                Quick Setup
              </q-badge>
            </div>
            <div class="text-subtitle2">Select your profile type to get started</div>
          </q-card-section>

          <q-card-section class="q-pa-lg">
            <div v-if="error" class="text-negative q-mb-md bg-red-1 q-pa-md rounded-borders">
              <q-icon name="error" />
              {{ error }}
            </div>

            <p class="text-body1 q-mb-lg">
              Creating a profile is the first step to connect with other innovators, investors,
              mentors, and professionals in our ecosystem. Select your profile type to get started.
            </p>

            <!-- Pre-selected Category Confirmation -->
            <div v-if="preSelectedCategory" class="q-mb-lg">
              <q-banner class="bg-light-green-1 text-dark rounded-borders">
                <template v-slot:avatar>
                  <q-icon name="check_circle" color="light-green-7" />
                </template>
                <div class="text-subtitle2">
                  You selected <strong>{{ preSelectedCategory.label }}</strong> profile
                </div>
                <div class="text-caption text-grey-8">
                  This category was pre-selected based on your sign-up choice
                </div>
              </q-banner>
            </div>

            <!-- Profile Type Selection -->
            <div class="q-mb-lg">
              <div class="text-subtitle1 q-mb-sm">
                Profile Category:
                <q-badge color="warning" class="q-ml-sm">Choose carefully - cannot be changed later</q-badge>
              </div>

              <q-select
                v-model="selectedProfileType"
                :options="profileTypeOptions"
                outlined
                emit-value
                map-options
                label="Select your profile type *"
              >
                <template v-slot:option="scope">
                  <q-item v-bind="scope.itemProps">
                    <q-item-section>
                      <q-item-label>{{ scope.opt.label }}</q-item-label>
                      <q-item-label caption>{{ getProfileTypeDescription(scope.opt.value) }}</q-item-label>
                    </q-item-section>
                  </q-item>
                </template>
              </q-select>

              <div class="text-caption q-mt-sm text-grey-8">
                Your profile category determines what information we collect and cannot be changed after profile creation.
                Choose the category that best represents your role in the innovation ecosystem.
              </div>
            </div>
          </q-card-section>

          <q-card-actions align="right" class="q-pa-md">
            <q-btn
              flat
              color="grey"
              label="Cancel"
              to="/dashboard"
              :disable="loading"
            />
            <q-btn
              color="primary"
              label="Proceed"
              :disable="!isFormValid || loading"
              @click="createProfile"
              :loading="loading"
            />
          </q-card-actions>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { useProfileStore } from '../../stores/profileStore'
import { useNotificationStore } from '../../stores/notifications'
import { getProfileTypeOptions, formatProfileType } from '../../services/profileTypes'
import { useUserState } from '../../services/userStateService'
import { useLoadingState } from '../../services/loadingStateService'
import { useCategoryService } from '../../services/categoryService'
import UnifiedIcon from '../../components/ui/UnifiedIcon.vue'

const router = useRouter()
const authStore = useAuthStore()
const profileStore = useProfileStore()
const notifications = useNotificationStore()
const { getSelectedCategory, getProfileTypeForCategory, clearSelectedCategory } = useCategoryService()

// State
const selectedProfileType = ref(null)
const error = ref(null)
const loadingMessage = ref('Creating your profile...')
const { isLoading, withLoading } = useLoadingState()
const loading = computed(() => isLoading('profileCreation'))
const preSelectedCategory = ref(null)

// Options for profile types
const profileTypeOptions = getProfileTypeOptions()

// Computed property to check if form is valid
const isFormValid = computed(() => {
  return !!selectedProfileType.value
})

// Function to get description for a profile type
function getProfileTypeDescription(type: string): string {
  const descriptions: Record<string, string> = {
    'innovator': 'Entrepreneurs, startups, and innovators with ideas or products.',
    'investor': 'Angel investors, VCs, and investment firms looking for opportunities.',
    'mentor': 'Experienced professionals offering guidance and mentorship.',
    'professional': 'Industry professionals looking to network and collaborate.',
    'industry_expert': 'Subject matter experts with specialized knowledge.',
    'academic_student': 'Students looking for opportunities and connections.',
    'academic_institution': 'Universities, colleges, and research institutions.',
    'organisation': 'Companies, NGOs, and other organizations.'
  }

  return descriptions[type] || ''
}

// Function to create a profile
async function createProfile() {
  if (!isFormValid.value) {
    notifications.warning('Please fill in all required fields')
    return
  }

  error.value = null

  try {
    await withLoading('profileCreation', async () => {
      loadingMessage.value = 'Creating your profile...'

      // Create the profile in the database
      const profileId = await profileStore.createProfile(
        selectedProfileType.value
      )

      if (profileId) {
        loadingMessage.value = 'Profile created! Redirecting...'
        notifications.success('Profile created! Now let\'s complete your profile details.')

        // Clear the selected category from localStorage since profile is created
        clearSelectedCategory()

        // Redirect to the profile edit page
        router.push({
          name: 'profile-edit',
          params: { id: profileId }
        })
      } else {
        throw new Error('Failed to create profile. The server did not return a profile ID.')
      }
    })
  } catch (err) {
    console.error('Error creating profile:', err)
    error.value = err.message || 'Failed to create profile. Please try again.'
    notifications.error(error.value)
  }
}

// Lifecycle
onMounted(async () => {
  error.value = null
  loadingMessage.value = 'Checking user status...'

  try {
    // Check if user is authenticated
    if (!authStore.isAuthenticated) {
      router.push('/sign-in')
      return
    }

    // Check if user already has a profile - if so, redirect to dashboard
    const { checkUserState, isNewUser } = useUserState()
    await checkUserState()

    if (!isNewUser.value) {
      notifications.info('You already have a profile. Redirecting to dashboard.')
      router.push('/dashboard')
      return
    }

    // Check for pre-selected category from localStorage
    const selectedCategory = getSelectedCategory()
    if (selectedCategory) {
      console.log('Found pre-selected category:', selectedCategory)
      preSelectedCategory.value = selectedCategory

      // Map category to profile type and pre-select it
      const profileType = getProfileTypeForCategory(selectedCategory.id)
      if (profileType) {
        selectedProfileType.value = profileType
        console.log('Pre-selected profile type:', profileType)
      }
    }

    // Pre-fill email if available
    if (authStore.currentUser?.email) {
      // This could be used later if we add an email field
      console.log('User email:', authStore.currentUser.email)
    }
  } catch (err) {
    console.error('Error during initialization:', err)
    error.value = 'Failed to initialize profile creation. Please refresh the page.'
  }
})
</script>

<style scoped>
.profile-creation-card {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.full-page-loader {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10;
}
</style>
