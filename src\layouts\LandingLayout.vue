<template>
  <q-layout view="hHh lpR lFf" class="landing-layout">
    <!-- Main Content -->
    <q-page-container>
      <router-view v-scroll-to-top></router-view>
    </q-page-container>

    <!-- <PERSON> Assistant -->
    <AIChatAssistant />

    <!-- <PERSON><PERSON> Banner -->
    <CookieConsentBanner />

    <!-- Unified Authentication Dialogs -->
    <UnifiedAuthDialogs />
  </q-layout>
</template>

<script lang="ts">
export default {
  name: 'LandingLayout'
};
</script>

<script setup lang="ts">
import AIChatAssistant from '../components/ai/AIChatAssistant.vue';
import CookieConsentBanner from '../components/legal/CookieConsentBanner.vue';
import UnifiedAuthDialogs from '../components/auth/UnifiedAuthDialogs.vue';
</script>

<style scoped>
.landing-layout {
  /* Minimal layout styles for the landing page without toolbar */
  min-height: 100vh;
  background: white;
}
</style>
