<template>
  <div class="auth-ui-test">
    <div class="test-container">
      <h1>Auth UI Test Page</h1>
      <p>This page is for testing the new authentication UI components.</p>
      
      <div class="test-buttons">
        <q-btn
          color="primary"
          label="Test Unified Sign Up Modal"
          @click="openUnifiedSignUp"
          class="q-mr-md q-mb-md"
        />
        
        <q-btn
          color="secondary"
          label="Test Sign In Modal"
          @click="openSignIn"
          class="q-mr-md q-mb-md"
        />
        
        <q-btn
          color="accent"
          label="Test Email Service"
          @click="testEmailService"
          class="q-mr-md q-mb-md"
          :loading="emailLoading"
        />
      </div>

      <div class="test-results" v-if="testResults.length > 0">
        <h3>Test Results:</h3>
        <div v-for="(result, index) in testResults" :key="index" class="test-result">
          <q-chip 
            :color="result.success ? 'positive' : 'negative'" 
            text-color="white"
            :label="result.success ? 'SUCCESS' : 'FAILED'"
          />
          <span class="q-ml-sm">{{ result.message }}</span>
          <div v-if="result.details" class="test-details">
            <pre>{{ JSON.stringify(result.details, null, 2) }}</pre>
          </div>
        </div>
      </div>

      <!-- Category Dropdown Test -->
      <div class="component-test">
        <h3>Category Dropdown Test</h3>
        <CategoryDropdown
          v-model="selectedCategoryId"
          @category-selected="handleCategorySelected"
        />
      </div>

      <!-- Category Selection Grid Test -->
      <div class="component-test">
        <h3>Category Selection Grid Test</h3>
        <CategorySelectionGrid
          :selected-category-id="selectedCategoryId"
          @category-selected="handleCategorySelected"
        />
      </div>

      <!-- Sign-In Method Selection Test -->
      <div class="component-test">
        <h3>Sign-In Method Selection Test</h3>
        <SignInMethodSelection
          :enabled="!!selectedCategoryId"
          :loading="false"
          @method-selected="handleMethodSelected"
          @form-submit="handleFormSubmit"
          @form-cancel="handleFormCancel"
        />
      </div>
    </div>

    <!-- Auth Dialogs -->
    <UnifiedAuthDialogs />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useQuasar } from 'quasar'
import CategoryDropdown from '@/components/auth/CategoryDropdown.vue'
import CategorySelectionGrid from '@/components/auth/CategorySelectionGrid.vue'
import SignInMethodSelection from '@/components/auth/SignInMethodSelection.vue'
import UnifiedAuthDialogs from '@/components/auth/UnifiedAuthDialogs.vue'
import { useUnifiedAuth } from '@/services/unifiedAuthService'
import { sendWelcomeEmail } from '@/services/emailService'
import type { CategoryOption } from '@/services/categoryService'

const $q = useQuasar()
const { openSignUpDialog, openSignInDialog } = useUnifiedAuth()

const selectedCategoryId = ref<string | null>(null)
const emailLoading = ref(false)
const testResults = ref<Array<{
  success: boolean
  message: string
  details?: any
}>>([])

const openUnifiedSignUp = () => {
  openSignUpDialog()
}

const openSignIn = () => {
  openSignInDialog()
}

const testEmailService = async () => {
  emailLoading.value = true
  try {
    const result = await sendWelcomeEmail('<EMAIL>', 'Test User')
    
    testResults.value.push({
      success: result.success,
      message: result.success ? 'Email service test successful' : 'Email service test failed',
      details: result
    })
    
    $q.notify({
      type: result.success ? 'positive' : 'negative',
      message: result.success ? 'Email test successful!' : 'Email test failed!',
      position: 'top'
    })
  } catch (error) {
    testResults.value.push({
      success: false,
      message: 'Email service test failed with error',
      details: error
    })
    
    $q.notify({
      type: 'negative',
      message: 'Email test failed!',
      position: 'top'
    })
  } finally {
    emailLoading.value = false
  }
}

const handleCategorySelected = (category: CategoryOption) => {
  selectedCategoryId.value = category.id
  console.log('Category selected:', category)
  
  testResults.value.push({
    success: true,
    message: `Category selected: ${category.label}`,
    details: category
  })
}

const handleMethodSelected = (method: string) => {
  console.log('Method selected:', method)
  
  testResults.value.push({
    success: true,
    message: `Sign-in method selected: ${method}`
  })
}

const handleFormSubmit = (formData: any) => {
  console.log('Form submitted:', formData)
  
  testResults.value.push({
    success: true,
    message: 'Form submitted successfully',
    details: formData
  })
}

const handleFormCancel = () => {
  console.log('Form cancelled')
  
  testResults.value.push({
    success: true,
    message: 'Form cancelled'
  })
}
</script>

<style scoped>
.auth-ui-test {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 2rem;
}

.test-container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.test-buttons {
  margin: 2rem 0;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.test-results {
  margin: 2rem 0;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.test-result {
  margin: 0.5rem 0;
  padding: 0.5rem;
  background: white;
  border-radius: 4px;
  display: flex;
  align-items: center;
}

.test-details {
  margin-top: 0.5rem;
  width: 100%;
}

.test-details pre {
  background: #f1f3f4;
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  overflow-x: auto;
}

.component-test {
  margin: 3rem 0;
  padding: 2rem;
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
}

.component-test h3 {
  margin-top: 0;
  color: #0D8A3E;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .auth-ui-test {
    padding: 1rem;
  }
  
  .test-container {
    padding: 1rem;
  }
  
  .test-buttons {
    flex-direction: column;
  }
  
  .component-test {
    padding: 1rem;
  }
}
</style>
