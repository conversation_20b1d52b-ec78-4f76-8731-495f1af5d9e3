<template>
  <q-dialog v-model="isOpen" persistent>
    <q-card class="signup-modal" style="min-width: 400px; max-width: 500px">
      <!-- Header -->
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">Join Our Platform</div>
        <q-space />
        <q-btn flat round dense @click="handleClose">
          <q-icon name="close" />
        </q-btn>
      </q-card-section>

      <!-- Content -->
      <q-card-section>
        <!-- Brief explanation and instruction -->
        <div class="q-mb-md">
          <p class="text-body2 text-grey-7 q-mb-sm">
            Choose a category below - you can join as an <strong>Innovator</strong>, <strong>Investor</strong>, <strong>Mentor</strong>, <strong>Industry Expert</strong>, <strong>Organisation</strong>, <strong>Academic Student</strong>, or <strong>Academic Institution</strong>
          </p>
        </div>

        <!-- Category Dropdown -->
        <div class="q-mb-lg">
          <q-select
            v-model="selectedCategory"
            :options="categoryOptions"
            option-value="id"
            option-label="label"
            emit-value
            map-options
            outlined
            label="Choose your category"
            dense
          >
            <template v-slot:prepend>
              <q-icon name="person" color="primary" />
            </template>
            <template v-slot:option="scope">
              <q-item v-bind="scope.itemProps">
                <q-item-section avatar>
                  <q-icon :name="scope.opt.icon" :color="getCategoryColor(scope.opt.id)" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>{{ scope.opt.label }}</q-item-label>
                  <q-item-label caption>{{ scope.opt.description }}</q-item-label>
                </q-item-section>
              </q-item>
            </template>
          </q-select>
        </div>

        <!-- Sign Up Form -->
        <q-form @submit="handleSubmit" class="q-gutter-md">
          <q-input
            v-model="form.email"
            type="email"
            label="Email"
            outlined
            dense
            :rules="emailRules"
          >
            <template v-slot:prepend>
              <q-icon name="email" color="primary" />
            </template>
          </q-input>

          <q-input
            v-model="form.password"
            :type="isPwd ? 'password' : 'text'"
            label="Password"
            outlined
            dense
            :rules="passwordRules"
          >
            <template v-slot:prepend>
              <q-icon name="lock" color="primary" />
            </template>
            <template v-slot:append>
              <q-icon
                :name="isPwd ? 'visibility_off' : 'visibility'"
                class="cursor-pointer"
                @click="isPwd = !isPwd"
                color="grey-6"
              />
            </template>
          </q-input>

          <q-checkbox
            v-model="form.acceptTerms"
            color="primary"
            dense
          >
            <span class="text-caption">
              I agree to the
              <router-link to="/legal/terms-conditions" target="_blank" class="text-primary">
                Terms
              </router-link>
              and
              <router-link to="/legal/privacy-policy" target="_blank" class="text-primary">
                Privacy Policy
              </router-link>
            </span>
          </q-checkbox>

          <q-btn
            type="submit"
            label="Create Account"
            :loading="loading"
            :disable="!form.acceptTerms || !selectedCategory"
            class="full-width"
            color="primary"
            no-caps
          />
        </q-form>
      </q-card-section>

      <!-- Footer -->
      <q-card-section class="text-center q-pt-none">
        <q-btn
          flat
          no-caps
          color="primary"
          @click="switchToSignIn"
          class="text-caption"
          size="sm"
        >
          Already have an account? Sign In
        </q-btn>
      </q-card-section>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useNotificationStore } from '@/stores/notifications'
import { useCategoryService } from '@/services/categoryService'
import { useUnifiedAuth } from '@/services/unifiedAuthService'

const authStore = useAuthStore()
const notificationStore = useNotificationStore()
const { getCategories, saveSelectedCategory } = useCategoryService()
const { state, closeAllDialogs, switchToSignIn, updateSignUpForm, handleSignUp } = useUnifiedAuth()

const isOpen = computed(() => state.value.isSignUpOpen)
const categoryOptions = getCategories()

const selectedCategory = ref<string | null>('innovator')
const loading = ref(false)
const isPwd = ref(true)

const form = ref({
  email: '',
  password: '',
  acceptTerms: false
})

// Color palette for categories
const categoryColors = {
  innovator: '#FF6B6B',
  mentor: '#4ECDC4',
  investor: '#45B7D1',
  industry_expert: '#96CEB4',
  organisation: '#F7DC6F',
  student: '#DDA0DD',
  academic_institution: '#98D8C8'
}

const getCategoryColor = (categoryId: string): string => {
  return categoryColors[categoryId as keyof typeof categoryColors] || '#0D8A3E'
}

// Validation rules
const emailRules = [
  (val: string) => !!val || 'Email is required',
  (val: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) || 'Please enter a valid email'
]

const passwordRules = [
  (val: string) => !!val || 'Password is required',
  (val: string) => val.length >= 6 || 'Password must be at least 6 characters'
]

const handleClose = () => {
  closeAllDialogs()
}

const handleSubmit = async () => {
  try {
    loading.value = true

    if (!selectedCategory.value) {
      notificationStore.error('Please select a category to continue.')
      return
    }

    console.log('Signing up with:', {
      email: form.value.email,
      category: selectedCategory.value
    })

    // Save the selected category
    saveSelectedCategory(selectedCategory.value)

    // Update the unified auth form with all data
    updateSignUpForm('email', form.value.email)
    updateSignUpForm('password', form.value.password)
    updateSignUpForm('acceptTerms', form.value.acceptTerms)

    // Find the category object and save it
    const categoryObject = categoryOptions.find(cat => cat.id === selectedCategory.value)
    if (categoryObject) {
      updateSignUpForm('selectedCategory', categoryObject)
    }

    // Use the unified auth service to handle sign up
    await handleSignUp()

    // Reset form
    form.value = {
      email: '',
      password: '',
      acceptTerms: false
    }
    selectedCategory.value = 'innovator' // Reset to default

    // The unified auth service will handle closing dialogs and navigation

  } catch (error: any) {
    console.error('Sign up error:', error)
    notificationStore.error(error.message || 'Registration failed. Please try again.')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.signup-modal {
  border-radius: 12px;
}

.signup-modal .q-card__section {
  padding: 1rem 1.5rem;
}
</style>
