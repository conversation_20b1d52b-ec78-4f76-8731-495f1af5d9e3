<template>
  <q-page class="q-pa-md">
    <!-- Full-page loading overlay -->
    <div v-if="initialLoading" class="full-page-loader">
      <q-spinner-dots color="primary" size="80px" />
      <div class="q-mt-md text-subtitle1">Loading profile data...</div>
    </div>

    <!-- Main content - only show when not in initial loading state -->
    <div v-else class="row q-col-gutter-md">
      <div class="col-12">
        <q-card>
          <q-card-section class="bg-light-green-8 text-white">
            <div class="row items-center">
              <div class="col">
                <div class="text-h6">
                  Edit Profile
                  <q-badge color="light-green-5" class="q-ml-sm">
                    Simplified Editor
                  </q-badge>
                </div>
                <div class="text-subtitle2">Update your profile information</div>
              </div>
              <div class="col-auto">
                <q-btn
                  flat
                  round
                  color="white"
                  :to="{ name: 'profile-view', params: { id: profile.user_id } }"
                  v-if="profile.user_id"
                >
                  <q-icon name="visibility" />
                  <q-tooltip>View Profile</q-tooltip>
                </q-btn>
              </div>
            </div>
          </q-card-section>

          <q-card-section>
            <div v-if="error" class="text-negative q-mb-md">
              <q-icon name="error" />
              {{ error }}
            </div>

            <div class="q-pa-md">
              <div class="text-h6 q-mb-md">Personal Details</div>

              <div class="row q-col-gutter-md">


                <!-- Profile Type -->
                <div class="col-12 col-md-6">
                  <q-select
                    v-model="profile.profile_type"
                    :options="profileTypeOptions"
                    label="Profile Type *"
                    outlined
                    emit-value
                    map-options
                    :disable="profileExists"
                  />
                </div>

                <!-- First Name -->
                <div class="col-12 col-md-6">
                  <q-input
                    v-model="profile.first_name"
                    label="First Name *"
                    outlined
                    :rules="[(val) => !!val || 'First name is required']"
                  />
                </div>

                <!-- Last Name -->
                <div class="col-12 col-md-6">
                  <q-input
                    v-model="profile.last_name"
                    label="Last Name *"
                    outlined
                    :rules="[(val) => !!val || 'Last name is required']"
                  />
                </div>

                <!-- Email -->
                <div class="col-12 col-md-6">
                  <q-input
                    v-model="profile.email"
                    label="Email *"
                    outlined
                    type="email"
                    readonly
                    disable
                  />
                </div>

                <!-- Phone -->
                <div class="col-12 col-md-6">
                  <q-input
                    v-model="profile.phone_number"
                    label="Phone Number"
                    outlined
                  />
                </div>
              </div>

              <div class="q-mt-lg">
                <q-btn
                  color="light-green-8"
                  label="Save Profile"
                  @click="saveProfile"
                  :loading="saving"
                />
                <q-btn
                  flat
                  color="grey"
                  label="Cancel"
                  class="q-ml-sm"
                  to="/dashboard"
                />
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '../../stores/auth'
import { useProfileStore } from '../../stores/profile'
import { useNotificationStore } from '../../stores/notifications'
import { supabase } from '../../lib/supabase'
import { getProfileTypeOptions } from '../../services/profileTypes'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const profileStore = useProfileStore()
const notifications = useNotificationStore()

// State
const initialLoading = ref(true)
const saving = ref(false)
const error = ref(null)
const profileExists = ref(false)
const profile = ref({
  id: '',
  user_id: '',
  email: '',
  first_name: '',
  last_name: '',
  profile_state: 'DRAFT',
  profile_type: null,
  profile_visibility: 'private',
  profile_completion: 0,
  bio: '',
  phone_number: '',
  phone_country_code: ''
})

// Options for select fields
const profileTypeOptions = getProfileTypeOptions()

// Save profile
async function saveProfile() {
  if (!profile.value.profile_type ||
      !profile.value.first_name || !profile.value.last_name) {
    notifications.error('Please fill in all required fields')
    return
  }

  saving.value = true
  error.value = null

  try {
    if (profileExists.value) {
      // Update existing profile
      const { error: updateError } = await supabase
        .from('personal_details')
        .update({
          profile_name: profile.value.profile_name,
          first_name: profile.value.first_name,
          last_name: profile.value.last_name,
          phone_number: profile.value.phone_number,
          phone_country_code: profile.value.phone_country_code,
          profile_completion: 100, // Mark as complete
          profile_state: 'ACTIVE',
          updated_at: new Date().toISOString()
        })
        .eq('user_id', profile.value.user_id)

      if (updateError) {
        throw updateError
      }
    } else {
      // Create new profile
      const { data, error: createError } = await supabase
        .from('personal_details')
        .insert({
          user_id: authStore.currentUser?.id,
          email: authStore.currentUser?.email,
          profile_name: profile.value.profile_name,
          profile_type: profile.value.profile_type,
          first_name: profile.value.first_name,
          last_name: profile.value.last_name,
          phone_number: profile.value.phone_number,
          phone_country_code: profile.value.phone_country_code,
          profile_completion: 100, // Mark as complete
          profile_state: 'ACTIVE',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()

      if (createError) {
        throw createError
      }

      // Create specialized profile
      if (profile.value.profile_type) {
        const tableName = `${profile.value.profile_type}_profiles`
        const { error: specializedError } = await supabase
          .from(tableName)
          .insert({
            user_id: authStore.currentUser?.id,
            profile_name: profile.value.profile_name,
            is_public: false,
            completion_percentage: 100,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })

        if (specializedError) {
          console.error('Error creating specialized profile:', specializedError)
          // Continue anyway, we've created the base profile
        }
      }
    }

    // Show success message
    notifications.success('Profile saved successfully!')

    // Refresh profile store
    await profileStore.loadUserProfiles()

    // Redirect to dashboard
    router.push('/dashboard')
  } catch (err) {
    console.error('Error saving profile:', err)
    error.value = err.message || 'An error occurred while saving your profile'
    notifications.error(error.value)
  } finally {
    saving.value = false
  }
}

// Load profile data
onMounted(async () => {
  initialLoading.value = true
  error.value = null

  if (!authStore.isAuthenticated) {
    router.push('/sign-in')
    return
  }

  try {
    // Get the profile ID from the route
    const profileId = route.params.id

    if (profileId) {
      // We're editing an existing profile
      profileExists.value = true

      // Get profile data
      const { data, error: fetchError } = await supabase
        .from('personal_details')
        .select('*')
        .eq('user_id', profileId)
        .single()

      if (fetchError) {
        throw fetchError
      }

      if (data) {
        // Set profile data
        profile.value = {
          ...profile.value,
          ...data
        }
      } else {
        throw new Error('Profile not found')
      }
    } else {
      // We're creating a new profile
      profileExists.value = false

      // Pre-fill email from auth store
      if (authStore.currentUser?.email) {
        profile.value.email = authStore.currentUser.email
      }
    }
  } catch (err) {
    console.error('Error loading profile:', err)
    error.value = err.message || 'An error occurred while loading your profile'
    notifications.error(error.value)
  } finally {
    initialLoading.value = false
  }
})
</script>

<style scoped>
.full-page-loader {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 10;
}
</style>
