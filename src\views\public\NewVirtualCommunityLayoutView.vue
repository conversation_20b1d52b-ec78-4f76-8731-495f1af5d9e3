<template>
  <div class="virtual-community-container">
    <!-- Main Content -->
    <div class="virtual-community-content">
      <!-- Feed Tab -->
      <div v-if="activeTab === 'feed'" class="tab-content">
        <div class="posts-grid">
          <PostCard
            v-for="post in posts"
            :key="post.id"
            :post="post"
            @like="handleLike"
            @comment="handleComment"
            @share="handleShare"
            @register="handleRegister"
            @apply="handleApply"
            @join="handleJoin"
            @contact="handleContact"
            @collaborate="handleCollaborate"
            @requestMentorship="handleRequestMentorship"
            @applyForFunding="handleApplyForFunding"
          />
        </div>

        <!-- Load More Button -->
        <div v-if="hasMore && posts.length > 0" class="load-more-container">
          <q-btn
            unelevated
            color="primary"
            label="Load More Posts"
            @click="loadMorePosts"
            :loading="loading"
          />
        </div>

        <!-- Empty State -->
        <div v-if="!loading && posts.length === 0" class="empty-state">
          <q-icon name="dynamic_feed" size="4rem" color="grey-5" />
          <h4>No Posts Yet</h4>
          <p>Be the first to share something with the community!</p>
        </div>
      </div>

      <!-- Profiles Tab -->
      <div v-else-if="activeTab === 'profiles'" class="tab-content">
        <!-- Refresh Button -->
        <div class="tab-header">
          <h5 class="tab-title">Profiles Directory</h5>
          <q-btn
            flat
            dense
            round
            icon="refresh"
            color="primary"
            @click="refreshProfiles"
            :loading="loadingProfiles"
            class="refresh-btn"
          >
            <q-tooltip>Refresh Profiles</q-tooltip>
          </q-btn>
        </div>

        <div class="profiles-grid">
          <ProfileCard
            v-for="profile in profiles"
            :key="profile.id"
            :profile="profile"
            @connect="handleConnect"
            @message="handleMessage"
          />
        </div>

        <!-- Load More Button -->
        <div v-if="hasMoreProfiles && profiles.length > 0" class="load-more-container">
          <q-btn
            unelevated
            color="primary"
            label="Load More Profiles"
            @click="loadMoreProfiles"
            :loading="loadingProfiles"
          />
        </div>

        <!-- Empty State -->
        <div v-if="!loadingProfiles && profiles.length === 0" class="empty-state">
          <q-icon name="people" size="4rem" color="grey-5" />
          <h4>No Profiles Found</h4>
          <p>No community members match your current filters.</p>
        </div>
      </div>

      <!-- Events Tab -->
      <div v-else-if="activeTab === 'events'" class="tab-content">
        <!-- Refresh Button -->
        <div class="tab-header">
          <h5 class="tab-title">Events</h5>
          <q-btn
            flat
            dense
            round
            icon="refresh"
            color="primary"
            @click="refreshEvents"
            :loading="loadingEvents"
            class="refresh-btn"
          >
            <q-tooltip>Refresh Events</q-tooltip>
          </q-btn>
        </div>

        <div class="events-grid">
          <EventCard
            v-for="event in events"
            :key="event.id"
            :event="event"
            @register="handleEventRegister"
            @share="handleShare"
          />
        </div>

        <!-- Load More Button -->
        <div v-if="hasMoreEvents && events.length > 0" class="load-more-container">
          <q-btn
            unelevated
            color="primary"
            label="Load More Events"
            @click="loadMoreEvents"
            :loading="loadingEvents"
          />
        </div>

        <!-- Empty State -->
        <div v-if="!loadingEvents && events.length === 0" class="empty-state">
          <q-icon name="event" size="4rem" color="grey-5" />
          <h4>No Events Found</h4>
          <p>No upcoming events match your current filters.</p>
        </div>
      </div>

      <!-- Blog Tab -->
      <div v-else-if="activeTab === 'blog'" class="tab-content">
        <BlogLayout
          :articles="articles"
          :loading="loadingArticles"
          :has-more="hasMoreArticles"
          :hide-featured-section="true"
          @load-more="loadMoreArticles"
          @refresh="refreshArticles"
        />
      </div>

      <!-- Marketplace Tab -->
      <div v-else-if="activeTab === 'marketplace'" class="tab-content">
        <!-- Refresh Button -->
        <div class="tab-header">
          <h5 class="tab-title">Marketplace</h5>
          <q-btn
            flat
            dense
            round
            icon="refresh"
            color="primary"
            @click="refreshMarketplace"
            :loading="loadingMarketplace"
            class="refresh-btn"
          >
            <q-tooltip>Refresh Marketplace</q-tooltip>
          </q-btn>
        </div>

        <div class="marketplace-grid">
          <MarketplaceCard
            v-for="item in marketplace"
            :key="item.id"
            :listing="item"
            @contact="handleContact"
            @share="handleShare"
          />
        </div>

        <!-- Load More Button -->
        <div v-if="hasMoreMarketplace && marketplace.length > 0" class="load-more-container">
          <q-btn
            unelevated
            color="primary"
            label="Load More Items"
            @click="loadMoreMarketplace"
            :loading="loadingMarketplace"
          />
        </div>

        <!-- Empty State -->
        <div v-if="!loadingMarketplace && marketplace.length === 0" class="empty-state">
          <q-icon name="store" size="4rem" color="grey-5" />
          <h4>No Items Found</h4>
          <p>No marketplace items match your current filters.</p>
        </div>
      </div>

      <!-- Groups Tab (Coming Soon) -->
      <div v-else-if="activeTab === 'groups'" class="tab-content">
        <div class="coming-soon-content">
          <q-icon name="group_work" size="4rem" color="grey-5" />
          <h4>Groups Coming Soon</h4>
          <p>We're working on bringing you community groups. Stay tuned!</p>
        </div>
      </div>
    </div>

    <!-- Filter Results Dialog -->
    <FilterResultsDialog
      v-model="showFilterResults"
      :active-tab="activeTab"
      :posts="posts"
      :events="events"
      :profiles="profiles"
      :articles="articles"
      :marketplace="marketplace"
      :loading="loading"
      :has-more="hasMore"
      @like="handleLike"
      @comment="handleComment"
      @share="handleShare"
      @contact="handleContact"
      @collaborate="handleCollaborate"
      @load-more="loadMore"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { useFilterStore } from '../../stores/filterStore'
import { useNotificationStore } from '../../stores/notifications'
import { useContentInteractions } from '../../composables/useContentInteractions'
import { useGlobalServicesStore } from '../../stores/globalServices'
import { useProfileStore } from '../../stores/profile'
import { useAuthStore } from '../../stores/auth'
import { feedDataService } from '../../services/feedDataService'


import PostCard from '../../components/feed/cards/PostCard.vue'
import ProfileCard from '../../components/feed/cards/ProfileCard.vue'
import EventCard from '../../components/feed/cards/EventCard.vue'
import MarketplaceCard from '../../components/feed/cards/MarketplaceCard.vue'
import BlogLayout from '../../components/blog/BlogLayout.vue'
import PostCreationDialog from '../../components/feed/PostCreationDialog.vue'
import FilterResultsDialog from '../../components/feed/FilterResultsDialog.vue'

// Composables
const route = useRoute()
const router = useRouter()
const $q = useQuasar()
const authStore = useAuthStore()
const profileStore = useProfileStore()
const filterStore = useFilterStore()
const notificationStore = useNotificationStore()
const globalServicesStore = useGlobalServicesStore()
const { likePost, sharePost, commentOnPost } = useContentInteractions()

// State
const posts = ref([])
const profiles = ref([])
const events = ref([])
const articles = ref([])
const marketplace = ref([])

const loading = ref(false)
const loadingProfiles = ref(false)
const loadingEvents = ref(false)
const loadingArticles = ref(false)
const loadingMarketplace = ref(false)


const hasMore = ref(true)
const hasMoreProfiles = ref(true)
const hasMoreEvents = ref(true)
const hasMoreArticles = ref(true)
const hasMoreMarketplace = ref(true)


const currentPage = ref(1)
const currentFilters = ref({})
const showFilterResults = ref(false)

// Computed
const activeTab = computed(() => {
  return route.query.tab as string || 'feed'
})

const aiContext = computed(() => ({
  tab: activeTab.value,
  filters: currentFilters.value,
  hasContent: {
    posts: posts.value.length > 0,
    profiles: profiles.value.length > 0,
    events: events.value.length > 0,
    articles: articles.value.length > 0,
    marketplace: marketplace.value.length > 0
  }
}))

// Methods
function handleTabChange(tab: string) {
  router.push({
    path: '/innovation-community',
    query: { ...route.query, tab }
  })
}



function handlePostCreated(post: any) {
  // Add new post to the beginning of the posts array
  posts.value.unshift(post)

  $q.notify({
    type: 'positive',
    message: 'Post created successfully!',
    position: 'top'
  })
}

function handleFilterChange(filters: any) {
  currentFilters.value = filters
  currentPage.value = 1

  // Update the filter store with the new filters
  filterStore.setSearchQuery(filters.searchQuery || '')

  // Update tab-specific filters and load content (normal behavior)
  switch (activeTab.value) {
    case 'feed':
      filterStore.updateFeedFilters({
        postTypes: filters.categories || [],
        categories: filters.categories || [],
        opportunityTypes: filters.categories || []
      })
      loadPosts()
      break
    case 'profiles':
      filterStore.updateProfileFilters({
        profileTypes: filters.profileTypes || []
      })
      loadProfiles()
      break
    case 'events':
      filterStore.updateEventFilters({
        eventTypes: filters.eventTypes || [],
        eventFormat: filters.eventTypes || []
      })
      loadEvents()
      break
    case 'blog':
      filterStore.updateBlogFilters({
        blogCategories: filters.blogCategories || [],
        readTime: filters.readTime || null
      })
      loadArticles()
      break
    case 'marketplace':
      filterStore.updateMarketplaceFilters({
        listingTypes: filters.marketplaceCategories || []
      })
      loadMarketplace()
      break
  }
}

// New function to handle filter results in dialog
async function handleFilterResults(filters: any) {
  console.log('Filter results requested with filters:', filters)
  currentFilters.value = filters
  currentPage.value = 1

  // Clear existing content to show fresh results
  posts.value = []
  events.value = []
  profiles.value = []
  articles.value = []
  marketplace.value = []

  // Update the filter store with the new filters
  filterStore.setSearchQuery(filters.searchQuery || '')

  try {
    // Perform database search based on active tab and filters
    switch (activeTab.value) {
      case 'feed':
        filterStore.updateFeedFilters({
          postTypes: filters.categories || [],
          categories: filters.categories || [],
          opportunityTypes: filters.categories || []
        })
        await loadPosts()
        break
      case 'profiles':
        filterStore.updateProfileFilters({
          profileTypes: filters.profileTypes || []
        })
        await loadProfiles()
        break
      case 'events':
        filterStore.updateEventFilters({
          eventTypes: filters.eventTypes || [],
          eventFormat: filters.eventTypes || []
        })
        await loadEvents()
        break
      case 'blog':
        filterStore.updateBlogFilters({
          blogCategories: filters.blogCategories || [],
          readTime: filters.readTime || null
        })
        await loadArticles()
        break
      case 'marketplace':
        filterStore.updateMarketplaceFilters({
          listingTypes: filters.marketplaceCategories || []
        })
        await loadMarketplace()
        break
    }

    // Show dialog with fresh results
    showFilterResults.value = true

    // Dispatch event for mobile filter component
    window.dispatchEvent(new CustomEvent('filter-results-loaded', {
      detail: {
        tab: activeTab.value,
        data: {
          posts: posts.value,
          events: events.value,
          profiles: profiles.value,
          articles: articles.value,
          marketplace: marketplace.value,
          loading: loading.value,
          hasMore: hasMore.value
        }
      }
    }))

    console.log('Filter results loaded, showing dialog')
  } catch (error) {
    console.error('Error loading filter results:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to load filter results'
    })
  }
}

// Content interaction methods
async function handleLike(postId: string) {
  try {
    await likePost(postId)
    // Update the post in the local array
    const post = posts.value.find(p => p.id === postId)
    if (post) {
      post.likes_count = (post.likes_count || 0) + 1
      post.user_has_liked = true
    }
  } catch (error) {
    console.error('Failed to like post:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to like post'
    })
  }
}

async function handleComment(data: { postId: string, comment: string }) {
  try {
    const { postId, comment } = data
    await commentOnPost(parseInt(postId), comment)

    $q.notify({
      type: 'positive',
      message: 'Comment added successfully!',
      position: 'top'
    })
  } catch (error) {
    console.error('Failed to comment on post:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to add comment'
    })
  }
}

async function handleShare(item: any) {
  try {
    await sharePost(item.id)
    $q.notify({
      type: 'positive',
      message: 'Shared successfully!'
    })
  } catch (error) {
    console.error('Failed to share:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to share'
    })
  }
}

async function handleConnect(profileId: string) {
  try {
    // Use the content interactions composable for connection logic
    const profile = profiles.value.find(p => p.id === profileId)
    if (!profile) {
      throw new Error('Profile not found')
    }

    // This would typically call a connection service
    console.log('Connect with profile:', profileId)

    $q.notify({
      type: 'positive',
      message: 'Connection request sent!',
      position: 'top'
    })
  } catch (error) {
    console.error('Failed to connect:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to send connection request'
    })
  }
}

async function handleMessage(profileId: string) {
  // This would typically open a message dialog
  console.log('Message profile:', profileId)
}

async function handleEventRegister(eventId: string) {
  // This would typically register for an event
  console.log('Register for event:', eventId)
  $q.notify({
    type: 'positive',
    message: 'Registered for event!'
  })
}

async function handleContact(itemId: string) {
  try {
    const item = marketplace.value.find(i => i.id === itemId)
    if (!item) {
      throw new Error('Item not found')
    }

    console.log('Contact seller for item:', itemId)

    $q.notify({
      type: 'positive',
      message: 'Message sent to seller!',
      position: 'top'
    })
  } catch (error) {
    console.error('Failed to contact seller:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to contact seller'
    })
  }
}

// Additional interaction methods from FeedContainer
async function handleRegister(eventId: string) {
  try {
    console.log('Register for event:', eventId)

    $q.notify({
      type: 'positive',
      message: 'Successfully registered for event!',
      position: 'top'
    })
  } catch (error) {
    console.error('Failed to register for event:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to register for event'
    })
  }
}

async function handleApply(opportunityId: string) {
  try {
    console.log('Apply for opportunity:', opportunityId)

    $q.notify({
      type: 'positive',
      message: 'Application submitted successfully!',
      position: 'top'
    })
  } catch (error) {
    console.error('Failed to apply for opportunity:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to submit application'
    })
  }
}

async function handleJoin(groupId: string) {
  try {
    console.log('Join group:', groupId)

    $q.notify({
      type: 'positive',
      message: 'Successfully joined group!',
      position: 'top'
    })
  } catch (error) {
    console.error('Failed to join group:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to join group'
    })
  }
}

async function handleCollaborate(postId: string) {
  try {
    console.log('Request collaboration for post:', postId)

    $q.notify({
      type: 'positive',
      message: 'Collaboration request sent!',
      position: 'top'
    })
  } catch (error) {
    console.error('Failed to request collaboration:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to send collaboration request'
    })
  }
}

async function handleRequestMentorship(postId: string) {
  try {
    console.log('Request mentorship from post:', postId)

    $q.notify({
      type: 'positive',
      message: 'Mentorship request sent!',
      position: 'top'
    })
  } catch (error) {
    console.error('Failed to request mentorship:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to send mentorship request'
    })
  }
}

async function handleApplyForFunding(postId: string) {
  try {
    console.log('Apply for funding from post:', postId)

    $q.notify({
      type: 'positive',
      message: 'Funding application submitted!',
      position: 'top'
    })
  } catch (error) {
    console.error('Failed to apply for funding:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to submit funding application'
    })
  }
}

// Load more content for dialog
function loadMore() {
  switch (activeTab.value) {
    case 'feed':
      loadMorePosts()
      break
    case 'profiles':
      loadMoreProfiles()
      break
    case 'events':
      loadMoreEvents()
      break
    case 'blog':
      loadMoreArticles()
      break
    case 'marketplace':
      loadMoreMarketplace()
      break
  }
}

// Content loading methods
async function loadPosts() {
  if (loading.value) return

  try {
    loading.value = true
    const result = await feedDataService.fetchFeedPosts(
      { page: currentPage.value, limit: 10 },
      filterStore.currentFilters
    )

    if (currentPage.value === 1) {
      posts.value = result.posts || []
    } else {
      posts.value.push(...(result.posts || []))
    }

    hasMore.value = result.hasMore || false
  } catch (error) {
    console.error('Failed to load posts:', error)
    $q.notify({
      type: 'negative',
      message: 'Failed to load posts'
    })
  } finally {
    loading.value = false
  }
}

async function loadProfiles() {
  if (loadingProfiles.value) return

  try {
    loadingProfiles.value = true
    const result = await feedDataService.fetchProfiles(
      { page: currentPage.value, limit: 10 },
      filterStore.currentFilters
    )

    if (currentPage.value === 1) {
      profiles.value = result.profiles || []
    } else {
      profiles.value.push(...(result.profiles || []))
    }

    hasMoreProfiles.value = result.hasMore || false
  } catch (error) {
    console.error('Failed to load profiles:', error)
  } finally {
    loadingProfiles.value = false
  }
}

async function loadEvents() {
  if (loadingEvents.value) return

  try {
    loadingEvents.value = true
    const result = await feedDataService.fetchEvents(
      { page: currentPage.value, limit: 10 },
      filterStore.currentFilters
    )

    if (currentPage.value === 1) {
      events.value = result.events || []
    } else {
      events.value.push(...(result.events || []))
    }

    hasMoreEvents.value = result.hasMore || false
  } catch (error) {
    console.error('Failed to load events:', error)
  } finally {
    loadingEvents.value = false
  }
}

async function loadArticles() {
  if (loadingArticles.value) return

  try {
    loadingArticles.value = true
    const result = await feedDataService.fetchArticles(
      { page: currentPage.value, limit: 10 },
      filterStore.currentFilters
    )

    if (currentPage.value === 1) {
      articles.value = result.articles || []
    } else {
      articles.value.push(...(result.articles || []))
    }

    hasMoreArticles.value = result.hasMore || false
  } catch (error) {
    console.error('Failed to load articles:', error)
  } finally {
    loadingArticles.value = false
  }
}

async function loadMarketplace() {
  if (loadingMarketplace.value) return

  try {
    loadingMarketplace.value = true
    const result = await feedDataService.fetchMarketplace(
      { page: currentPage.value, limit: 10 },
      filterStore.currentFilters
    )

    if (currentPage.value === 1) {
      marketplace.value = result.marketplace || []
    } else {
      marketplace.value.push(...(result.marketplace || []))
    }

    hasMoreMarketplace.value = result.hasMore || false
  } catch (error) {
    console.error('Failed to load marketplace:', error)
  } finally {
    loadingMarketplace.value = false
  }
}

// Load more methods
function loadMorePosts() {
  if (hasMore.value && !loading.value) {
    currentPage.value++
    loadPosts()
  }
}

function loadMoreProfiles() {
  if (hasMoreProfiles.value && !loadingProfiles.value) {
    currentPage.value++
    loadProfiles()
  }
}

function loadMoreEvents() {
  if (hasMoreEvents.value && !loadingEvents.value) {
    currentPage.value++
    loadEvents()
  }
}

function loadMoreArticles() {
  if (hasMoreArticles.value && !loadingArticles.value) {
    currentPage.value++
    loadArticles()
  }
}

function loadMoreMarketplace() {
  if (hasMoreMarketplace.value && !loadingMarketplace.value) {
    currentPage.value++
    loadMarketplace()
  }
}

// Refresh methods

function refreshArticles() {
  currentPage.value = 1
  loadArticles()
}

function refreshProfiles() {
  currentPage.value = 1
  loadProfiles()
}

function refreshEvents() {
  currentPage.value = 1
  loadEvents()
}

function refreshMarketplace() {
  currentPage.value = 1
  loadMarketplace()
}

// Content loading based on tab
async function loadContent(tab: string) {
  currentPage.value = 1

  switch (tab) {
    case 'feed':
      await loadPosts()
      break
    case 'profiles':
      await loadProfiles()
      break
    case 'events':
      await loadEvents()
      break
    case 'blog':
      await loadArticles()
      break
    case 'marketplace':
      await loadMarketplace()
      break
  }
}

// Watch for tab changes
watch(activeTab, (newTab, oldTab) => {
  // Clear filters when switching tabs
  if (oldTab && newTab !== oldTab) {
    console.log(`Tab changed from ${oldTab} to ${newTab}, clearing filters`)
    filterStore.resetCurrentTabFilters()
    currentFilters.value = {}
    currentPage.value = 1
  }
  loadContent(newTab)
})

// Watch for filter store changes
watch(() => filterStore.currentFilters, (newFilters) => {
  console.log('Filter store changed, reloading content:', newFilters)
  currentPage.value = 1
  loadContent(activeTab.value)
}, { deep: true })

// Listen for filter-results events from parent layout
onMounted(() => {
  // Listen for filter-results events from the layout
  const handleLayoutFilterResults = (event: CustomEvent) => {
    handleFilterResults(event.detail)
  }

  window.addEventListener('filter-results', handleLayoutFilterResults)

  // Cleanup on unmount
  onUnmounted(() => {
    window.removeEventListener('filter-results', handleLayoutFilterResults)
  })
})

// Initialize filters from URL query parameters
function initializeFiltersFromQuery() {
  const query = route.query

  // Handle profile types from URL
  if (query.profileTypes && activeTab.value === 'profiles') {
    const profileTypes = typeof query.profileTypes === 'string'
      ? query.profileTypes.split(',')
      : query.profileTypes

    filterStore.updateProfileFilters({
      profileTypes: profileTypes
    })

    console.log('🔍 Initialized profile filters from URL:', profileTypes)
  }

  // Handle other filter types as needed
  if (query.eventTypes && activeTab.value === 'events') {
    const eventTypes = typeof query.eventTypes === 'string'
      ? query.eventTypes.split(',')
      : query.eventTypes

    filterStore.updateEventFilters({
      eventTypes: eventTypes
    })
  }

  if (query.categories && activeTab.value === 'feed') {
    const categories = typeof query.categories === 'string'
      ? query.categories.split(',')
      : query.categories

    filterStore.updateFeedFilters({
      categories: categories
    })
  }
}

// Lifecycle
onMounted(() => {
  console.log('🚀 NewVirtualCommunityLayoutView mounted, activeTab:', activeTab.value)

  // Initialize filters from URL query parameters first
  initializeFiltersFromQuery()

  // Then load content with the initialized filters
  loadContent(activeTab.value)
})

// Watch for query parameter changes
watch(() => route.query, () => {
  initializeFiltersFromQuery()
}, { deep: true })
</script>

<style scoped>
.virtual-community-content {
  min-height: 400px;
}

.tab-content {
  animation: fadeIn 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 0 8px; /* Add small horizontal padding for tab content */
}

/* Events tab specific padding for desktop */
@media (min-width: 769px) {
  .tab-content:has(.events-grid) {
    padding: 0 24px; /* Increased padding for events tab on desktop */
  }
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 8px 8px 8px;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 8px;
}

.tab-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
}

.refresh-btn {
  color: #6b7280;

  &:hover {
    color: #0D8A3E;
    background: rgba(13, 138, 62, 0.1);
  }
}

/* Content grids */
.posts-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.profiles-grid,
.events-grid,
.marketplace-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
  max-width: 800px; /* Limit width to ensure max 2 columns */
  margin: 0 auto;
}

/* Profile card styling */
.profiles-grid article {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.2s ease;
  position: relative;
}

.profiles-grid article:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

/* Profile type indicators */
.profiles-grid article[data-profile-type="innovator"] {
  border-top: 4px solid #5b8def;
}

.profiles-grid article[data-profile-type="academic_student"] {
  border-top: 4px solid #6ee7b7;
}

.profiles-grid article[data-profile-type="mentor"] {
  border-top: 4px solid #f472b6;
}

.profiles-grid article[data-profile-type="entrepreneur"] {
  border-top: 4px solid #fbbf24;
}

/* Event card styling */
.events-grid article {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.2s ease;
}

.events-grid article:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

/* Marketplace card styling */
.marketplace-grid article {
  background: white;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  overflow: hidden;
  transition: all 0.2s ease;
}

.marketplace-grid article:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

/* Empty state styling */
.empty-state {
  text-align: center;
  padding: 48px 24px;
  color: #6b7280;
}

.empty-state h4 {
  margin: 16px 0 8px 0;
  color: #374151;
  font-weight: 500;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* Load more button styling */
.load-more-container {
  text-align: center;
  padding: 24px;
}

.load-more-container .q-btn {
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
}

/* Coming soon content styling */
.coming-soon-content {
  text-align: center;
  padding: 48px 24px;
  color: #6b7280;
}

.coming-soon-content h4 {
  margin: 16px 0 8px 0;
  color: #374151;
  font-weight: 500;
}

.coming-soon-content p {
  margin: 0;
  font-size: 14px;
}



.profiles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  max-width: 700px; /* Limit to max 2 columns */
  margin: 0 auto;
}

.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  max-width: 900px; /* Increased max width */
  margin: 0 auto;
  padding: 20px 24px; /* Increased padding to prevent cutoff */
}

.marketplace-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  max-width: 700px; /* Limit to max 2 columns */
  margin: 0 auto;
}

/* Mobile responsive - force single column */
@media (max-width: 768px) {
  .profiles-grid,
  .events-grid,
  .marketplace-grid {
    grid-template-columns: 1fr;
    max-width: 100%;
    gap: 12px;
    padding: 0 8px; /* Reduced padding on mobile */
  }
}

/* Load more and empty states */
.load-more-container {
  text-align: center;
  padding: 24px;
  margin-top: 16px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-state h4 {
  margin: 16px 0 8px 0;
  color: #374151;
}

.empty-state p {
  margin: 0 0 24px 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.coming-soon-content {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.coming-soon-content h4 {
  margin: 16px 0 8px 0;
  color: #374151;
}

.coming-soon-content p {
  margin: 0 0 24px 0;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}



@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
