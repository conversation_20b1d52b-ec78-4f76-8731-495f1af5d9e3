<template>
  <div class="method-selection-step">
    <!-- Header Section -->
    <div class="step-header">
      <div class="step-header__content">
        <h1 class="step-title">
          Choose your sign-up method
        </h1>
        <p class="step-subtitle">
          You've selected <strong>{{ selectedCategoryLabel }}</strong>. 
          Now choose how you'd like to create your account.
        </p>
      </div>
    </div>

    <!-- Selected Category Display -->
    <div class="selected-category">
      <div class="glass-container category-display">
        <div class="category-display__content">
          <div class="category-display__icon">
            <unified-icon 
              :name="selectedCategoryIcon" 
              size="2rem"
              color="primary"
            />
          </div>
          <div class="category-display__info">
            <div class="category-display__title">{{ selectedCategoryLabel }}</div>
            <div class="category-display__description">{{ selectedCategoryDescription }}</div>
          </div>
          <q-btn
            flat
            round
            dense
            icon="edit"
            color="primary"
            @click="handleChangeCategory"
            class="category-display__edit"
            aria-label="Change category"
          />
        </div>
      </div>
    </div>

    <!-- Sign-In Methods -->
    <div class="step-body">
      <div class="glass-container">
        <div class="methods-grid">
          <!-- Email & Password Method -->
          <div 
            class="method-card glass-card glass-card--interactive"
            @click="handleEmailPasswordMethod"
            @keydown.enter="handleEmailPasswordMethod"
            @keydown.space.prevent="handleEmailPasswordMethod"
            tabindex="0"
            role="button"
            aria-label="Sign up with email and password"
          >
            <div class="method-card__content">
              <div class="method-card__icon">
                <unified-icon name="mail" size="2.5rem" color="primary" />
              </div>
              <div class="method-card__title">Email & Password</div>
              <div class="method-card__description">
                Create an account with your email address and a secure password
              </div>
              <div class="method-card__badge">
                <q-badge color="primary" label="Available" />
              </div>
            </div>
          </div>

          <!-- Gmail Method (Coming Soon) -->
          <div class="method-card glass-card method-card--disabled">
            <div class="method-card__content">
              <div class="method-card__icon">
                <unified-icon name="mail" size="2.5rem" color="grey-6" />
              </div>
              <div class="method-card__title">Continue with Gmail</div>
              <div class="method-card__description">
                Sign up quickly using your Google account
              </div>
              <div class="method-card__badge">
                <q-badge color="grey-6" label="Coming Soon" />
              </div>
            </div>
          </div>

          <!-- Phone Number Method (Coming Soon) -->
          <div class="method-card glass-card method-card--disabled">
            <div class="method-card__content">
              <div class="method-card__icon">
                <unified-icon name="phone" size="2.5rem" color="grey-6" />
              </div>
              <div class="method-card__title">Phone Number</div>
              <div class="method-card__description">
                Sign up with your phone number and SMS verification
              </div>
              <div class="method-card__badge">
                <q-badge color="grey-6" label="Coming Soon" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer Section -->
    <div class="step-footer">
      <div class="step-footer__content">
        <div class="footer-info">
          <p class="footer-text">
            <unified-icon name="security" size="1rem" color="primary" />
            Your information is secure and will never be shared
          </p>
        </div>
        
        <div class="footer-actions">
          <q-btn
            flat
            no-caps
            color="grey-6"
            @click="handleBack"
            class="glass-btn"
          >
            <unified-icon name="arrow_back" size="1rem" />
            Back
          </q-btn>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import UnifiedIcon from '@/components/ui/UnifiedIcon.vue'
import { useCategoryService } from '@/services/categoryService'

interface Props {
  selectedCategoryId?: string | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'method-selected': [method: string]
  'change-category': []
  'back': []
}>()

const { getCategoryById } = useCategoryService()

// Computed properties
const selectedCategory = computed(() => {
  return props.selectedCategoryId ? getCategoryById(props.selectedCategoryId) : null
})

const selectedCategoryLabel = computed(() => {
  return selectedCategory.value?.label || 'Unknown Category'
})

const selectedCategoryDescription = computed(() => {
  return selectedCategory.value?.description || ''
})

const selectedCategoryIcon = computed(() => {
  return selectedCategory.value?.icon || 'help'
})

// Methods
const handleEmailPasswordMethod = () => {
  emit('method-selected', 'email-password')
}

const handleChangeCategory = () => {
  emit('change-category')
}

const handleBack = () => {
  emit('back')
}
</script>

<style scoped>
.method-selection-step {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 100vh;
}

.step-header {
  flex-shrink: 0;
  padding: 2rem 0 1rem;
  text-align: center;
}

.step-header__content {
  max-width: 800px;
  margin: 0 auto;
}

.step-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.step-subtitle {
  font-size: 1.25rem;
  color: #666;
  margin: 0;
  line-height: 1.4;
}

.selected-category {
  flex-shrink: 0;
  padding: 1rem 0;
}

.category-display {
  max-width: 600px;
  margin: 0 auto;
  padding: 1.5rem;
}

.category-display__content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.category-display__icon {
  flex-shrink: 0;
}

.category-display__info {
  flex: 1;
}

.category-display__title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 0.25rem;
}

.category-display__description {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
}

.category-display__edit {
  flex-shrink: 0;
}

.step-body {
  flex: 1;
  padding: 1rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.methods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  padding: 1rem 0;
  max-width: 1000px;
  width: 100%;
}

.method-card {
  padding: 2rem 1.5rem;
  text-align: center;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
  position: relative;
}

.method-card--disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.method-card--disabled:hover {
  transform: none;
  box-shadow: var(--glass-shadow);
}

.method-card__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  width: 100%;
  height: 100%;
}

.method-card__icon {
  margin-bottom: 0.5rem;
}

.method-card__title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 0.5rem;
}

.method-card__description {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
  text-align: center;
  max-width: 250px;
}

.method-card__badge {
  margin-top: 0.5rem;
}

.step-footer {
  flex-shrink: 0;
  padding: 2rem 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.step-footer__content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
}

.footer-info {
  flex: 1;
}

.footer-text {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0;
  font-size: 0.9rem;
  color: #666;
}

.footer-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .step-title {
    font-size: 2rem;
  }
  
  .step-subtitle {
    font-size: 1.1rem;
  }
  
  .methods-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .category-display__content {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .step-footer__content {
    flex-direction: column;
    gap: 1.5rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .step-header {
    padding: 1.5rem 0 1rem;
  }
  
  .step-title {
    font-size: 1.75rem;
  }
  
  .step-subtitle {
    font-size: 1rem;
  }
  
  .method-card {
    padding: 1.5rem 1rem;
    min-height: 180px;
  }
  
  .step-footer {
    padding: 1.5rem 0;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .step-title,
  .category-display__title,
  .method-card__title {
    color: #000;
  }
  
  .step-subtitle,
  .category-display__description,
  .method-card__description,
  .footer-text {
    color: #333;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .step-title,
  .category-display__title,
  .method-card__title {
    color: #f0f0f0;
  }
  
  .step-subtitle,
  .category-display__description,
  .method-card__description,
  .footer-text {
    color: #ccc;
  }
  
  .step-footer {
    background: rgba(30, 30, 30, 0.95);
    border-top-color: rgba(255, 255, 255, 0.1);
  }
}
</style>
