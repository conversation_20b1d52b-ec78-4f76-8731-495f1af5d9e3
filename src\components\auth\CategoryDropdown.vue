<template>
  <div class="category-dropdown">
    <!-- Dropdown Selection -->
    <q-select
      v-model="selectedCategory"
      :options="categoryOptions"
      option-value="id"
      option-label="label"
      emit-value
      map-options
      outlined
      label="Choose your category"
      class="category-select"
      :class="{ 'has-selection': !!selectedCategory }"
      @update:model-value="handleCategoryChange"
    >
      <template v-slot:prepend>
        <unified-icon 
          :name="getSelectedCategoryIcon()" 
          :color="selectedCategory ? '#0D8A3E' : '#666'"
          size="1.2rem"
        />
      </template>

      <template v-slot:option="scope">
        <q-item v-bind="scope.itemProps" class="category-option">
          <q-item-section avatar>
            <div class="category-icon-wrapper">
              <unified-icon 
                :name="scope.opt.icon" 
                :color="getCategoryColor(scope.opt.id)"
                size="1.5rem"
              />
            </div>
          </q-item-section>
          <q-item-section>
            <q-item-label class="category-label">{{ scope.opt.label }}</q-item-label>
            <q-item-label caption class="category-description">
              {{ scope.opt.description }}
            </q-item-label>
          </q-item-section>
        </q-item>
      </template>

      <template v-slot:selected-item="scope">
        <div class="selected-category-display">
          <unified-icon 
            :name="scope.opt.icon" 
            color="#0D8A3E"
            size="1.2rem"
            class="q-mr-sm"
          />
          <span class="selected-label">{{ scope.opt.label }}</span>
        </div>
      </template>
    </q-select>

    <!-- Selected Category Details -->
    <div v-if="selectedCategoryDetails" class="selected-category-info">
      <div class="info-card glass-card">
        <div class="info-header">
          <unified-icon 
            :name="selectedCategoryDetails.icon" 
            :color="getCategoryColor(selectedCategoryDetails.id)"
            size="1.5rem"
          />
          <h4>{{ selectedCategoryDetails.label }}</h4>
        </div>
        <p class="info-description">{{ getDetailedDescription(selectedCategoryDetails.id) }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import UnifiedIcon from '@/components/ui/UnifiedIcon.vue'
import { useCategoryService, type CategoryOption } from '@/services/categoryService'

interface Props {
  modelValue?: string | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'update:modelValue': [value: string | null]
  'category-selected': [category: CategoryOption | null]
}>()

const { getCategories, getCategoryById } = useCategoryService()

const selectedCategory = ref<string | null>(props.modelValue || null)
const categoryOptions = getCategories()

// Color palette for different categories
const categoryColors = {
  innovator: '#FF6B6B',
  mentor: '#4ECDC4', 
  investor: '#45B7D1',
  industry_expert: '#96CEB4',
  academic: '#FFEAA7',
  student: '#DDA0DD',
  academic_institution: '#98D8C8',
  government_organisation: '#F7DC6F'
}

const selectedCategoryDetails = computed(() => {
  return selectedCategory.value ? getCategoryById(selectedCategory.value) : null
})

const getSelectedCategoryIcon = () => {
  if (!selectedCategory.value) return 'person'
  const category = getCategoryById(selectedCategory.value)
  return category?.icon || 'person'
}

const getCategoryColor = (categoryId: string): string => {
  return categoryColors[categoryId as keyof typeof categoryColors] || '#0D8A3E'
}

const handleCategoryChange = (value: string | null) => {
  selectedCategory.value = value
  emit('update:modelValue', value)
  
  const category = value ? getCategoryById(value) : null
  emit('category-selected', category)
}

// Enhanced descriptions for each category
const getDetailedDescription = (categoryId: string): string => {
  const descriptions = {
    innovator: 'Perfect for entrepreneurs, startup founders, and creative minds with groundbreaking ideas. Connect with mentors, investors, and fellow innovators to bring your vision to life.',
    mentor: 'Ideal for experienced professionals who want to guide the next generation. Share your expertise, build meaningful relationships, and help shape the future of innovation.',
    investor: 'Designed for angel investors, VCs, and funding organizations. Discover promising startups, evaluate investment opportunities, and build a diverse portfolio.',
    industry_expert: 'Great for specialists with deep domain knowledge. Share insights, consult on projects, and establish yourself as a thought leader in your field.',
    academic: 'Perfect for universities, colleges, research institutions, and academic professionals. Collaborate on research, share knowledge, and bridge the gap between academia and industry.',
    student: 'Ideal for students seeking opportunities, mentorship, and real-world experience. Connect with professionals, find internships, and accelerate your career.',
    academic_institution: 'Designed for universities, colleges, and research institutions. Showcase programs, connect with industry partners, and promote academic excellence.',
    government_organisation: 'Perfect for government agencies and public sector organizations. Foster innovation, support economic development, and create public-private partnerships.'
  }
  
  return descriptions[categoryId as keyof typeof descriptions] || 'Join our community and connect with like-minded professionals.'
}

// Watch for prop changes
watch(() => props.modelValue, (newValue) => {
  selectedCategory.value = newValue
})
</script>

<style scoped>
.category-dropdown {
  width: 100%;
}

.category-select {
  margin-bottom: 1rem;
}

.category-select.has-selection :deep(.q-field__control) {
  border-color: #0D8A3E;
}

.category-option {
  padding: 12px 16px;
  transition: all 0.2s ease;
}

.category-option:hover {
  background-color: rgba(13, 138, 62, 0.05);
}

.category-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.category-label {
  font-weight: 600;
  color: #333;
  font-size: 1rem;
}

.category-description {
  color: #666;
  font-size: 0.85rem;
  line-height: 1.3;
  margin-top: 2px;
}

.selected-category-display {
  display: flex;
  align-items: center;
}

.selected-label {
  font-weight: 500;
  color: #0D8A3E;
}

.selected-category-info {
  margin-top: 1rem;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.info-card {
  padding: 1.5rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.info-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.info-header h4 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: #0D8A3E;
}

.info-description {
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #555;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .category-option {
    padding: 10px 12px;
  }
  
  .category-icon-wrapper {
    width: 2rem;
    height: 2rem;
  }
  
  .category-label {
    font-size: 0.95rem;
  }
  
  .category-description {
    font-size: 0.8rem;
  }
  
  .info-card {
    padding: 1rem;
  }
  
  .info-header h4 {
    font-size: 1rem;
  }
  
  .info-description {
    font-size: 0.85rem;
  }
}
</style>
