import { defineStore } from 'pinia'
import { supabase } from '../lib/supabase'
import type { User } from '@supabase/supabase-js'
import router from '../router'
import { useNotificationStore } from '../stores/notifications'
import { ref, computed } from 'vue'
import { createPersonalDetails, checkPersonalDetailsTable } from '../lib/databaseSetup'
import { sendWelcomeEmail, sendPasswordResetEmail } from '../services/emailService'

/**
 * Updates personal details if they're missing required fields
 */
async function updatePersonalDetailsIfNeeded(userId: string, details: any): Promise<void> {
  try {
    // Check if any required fields are missing
    const requiredFields = {
      profile_name: 'My Profile',
      profile_state: 'IN_PROGRESS',
      profile_visibility: 'private',
      is_verified: false,
      profile_completion: 0
    }

    const updateData: Record<string, any> = {}
    let needsUpdate = false

    // Check each required field
    for (const [field, defaultValue] of Object.entries(requiredFields)) {
      if (details[field] === undefined || details[field] === null) {
        updateData[field] = defaultValue
        needsUpdate = true
      }
    }

    // If any fields are missing, update the record
    if (needsUpdate) {
      console.log('Updating personal details with missing fields:', updateData)
      const { error } = await supabase
        .from('personal_details')
        .update({
          ...updateData,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)

      if (error) {
        console.error('Error updating personal details:', error.message)
      } else {
        console.log('Personal details updated successfully')
      }
    }
  } catch (error: any) {
    console.error('Error in updatePersonalDetailsIfNeeded:', error.message)
  }
}

// Authentication store with database integration
export const useAuthStore = defineStore('auth', () => {
  const notifications = useNotificationStore()

  const user = ref<User | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const isInitialized = ref(false)

  const isAuthenticated = computed(() => !!user.value)
  const currentUser = computed(() => user.value)

  // Simplified signup function that works without email verification
  async function signUp(userData: {
    email: string
    password: string
  }): Promise<{ user: User } | null> {
    loading.value = true
    error.value = null
    try {
      console.log('Starting simplified signup process...')

      // Basic validation
      if (!userData.email || !userData.password) {
        throw new Error('Email and password are required')
      }

      // Email validation with a more permissive regex
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(userData.email)) {
        throw new Error('Please enter a valid email address')
      }

      // Password validation
      if (userData.password.length < 6) {
        throw new Error('Password must be at least 6 characters long')
      }

      // Two-step approach: First create the user with admin API
      console.log('Attempting direct user creation...')

      // Create the user with regular signup
      console.log('Attempting regular signup...')
      const { data: regularData, error: regularError } = await supabase.auth.signUp({
        email: userData.email,
        password: userData.password,
        options: {
          // Email confirmation is disabled in Supabase, but we'll still handle errors gracefully
          emailRedirectTo: `${window.location.origin}/auth/verify`
        }
      })

      // Handle specific error cases
      if (regularError) {
        console.error('Signup error:', regularError)

        // Check for "user already exists" errors
        if (regularError.message?.includes('User already registered') ||
            regularError.message?.includes('already been registered') ||
            regularError.message?.includes('email address is already registered') ||
            regularError.message?.includes('already registered') ||
            regularError.code === 'user_already_exists') {
          // User exists, suggest sign in instead
          throw new Error('An account with this email already exists. Please sign in instead or use the "Forgot Password" option if you need to reset your password.')
        }

        // Check for email format errors
        if (regularError.message?.includes('Unable to validate email address') ||
            regularError.message?.includes('invalid format')) {
          throw new Error('Please enter a valid email address.')
        }

        // Check for password strength errors
        if (regularError.message?.includes('Password should be at least')) {
          throw new Error(regularError.message) // Use the original message as it's clear
        }

        // If it's an email confirmation error, try to continue
        if (regularError.message.includes('confirmation email') ||
            regularError.message.includes('unexpected_failure')) {
          console.warn('Email confirmation failed but continuing with signup:', regularError)

          // Try to sign in immediately since email confirmation is disabled
          try {
            const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
              email: userData.email,
              password: userData.password
            })

            if (!signInError && signInData.user) {
              console.log('Successfully signed in after email confirmation error')
              return { user: signInData.user }
            }
          } catch (signInError) {
            console.warn('Failed to sign in after email confirmation error:', signInError)
          }
        } else {
          // For any other error, throw it
          throw regularError
        }
      }

      // Check if we got a user back
      if (!regularData?.user) {
        throw new Error('No user data returned from signup')
      }

      // Use the regular signup data
      const data = { user: regularData.user }

      if (!data.user) {
        throw new Error('No user data returned from signup')
      }

      console.log('User created successfully:', data.user.id)

      // Set user state
      user.value = data.user

      // Step 2: Create personal details record
      console.log('Creating personal details record...')
      const createResult = await createPersonalDetails(data.user.id, data.user.email || '')
      if (!createResult.success) {
        console.warn('Personal details creation warning:', createResult.message)
      }

      // Step 3: Try to sign in the user immediately with multiple attempts
      try {
        console.log('Attempting to sign in user after signup...')
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email: userData.email,
          password: userData.password
        })

        if (signInError) {
          console.warn('First sign-in attempt failed, trying again after delay:', signInError)

          // Wait a moment and try again
          await new Promise(resolve => setTimeout(resolve, 1500))

          // Second attempt
          const { data: retryData, error: retryError } = await supabase.auth.signInWithPassword({
            email: userData.email,
            password: userData.password
          })

          if (retryError) {
            console.warn('Second sign-in attempt also failed:', retryError)
            // We'll still continue with the flow
          } else if (retryData.user) {
            console.log('Successfully signed in user on second attempt')
            user.value = retryData.user
          }
        } else if (signInData.user) {
          console.log('Successfully signed in user after signup')
          user.value = signInData.user
        }
      } catch (signInError) {
        console.warn('Error during automatic sign in after signup:', signInError)
        // Don't throw error here, as the signup was successful
      }

      // Send welcome email
      try {
        console.log('Sending welcome email to:', data.user.email)
        const { sendWelcomeEmail } = await import('../services/emailService')
        const emailResult = await sendWelcomeEmail(data.user.email || '')

        if (emailResult.success) {
          console.log('Welcome email sent successfully')
          notifications.success('Welcome email sent successfully!')
        } else {
          console.error('Failed to send welcome email:', emailResult.error)
          notifications.warning('Account created successfully, but welcome email could not be sent.')
        }
      } catch (emailError) {
        console.error('Error sending welcome email:', emailError)
        console.error('Email error details:', JSON.stringify(emailError))
        notifications.warning('Account created successfully, but welcome email could not be sent.')
        // Don't throw error here, as the signup was successful
      }

      // Success message - navigation is handled by the calling service
      notifications.success('Registration successful! You can now use the platform.')

      // Note: Navigation is now handled by the unified auth service
      // to support the new modal-based post-registration flow

      // Return the user data
      return { user: data.user }

    } catch (error: any) {
      console.error('Signup error:', error)

      // Handle specific error messages with improved user feedback
      if (error.message && (error.message.includes('User already registered') ||
                           error.message.includes('already been registered') ||
                           error.message.includes('email address is already registered') ||
                           error.message.includes('already registered'))) {
        notifications.error('An account with this email already exists. Please sign in instead or use the "Forgot Password" option if you need to reset your password.')
        // Don't auto-redirect, let user choose their action
      } else if (error.message && error.message.includes('Email rate limit exceeded')) {
        notifications.error('Too many signup attempts. Please try again later.')
      } else if (error.message && error.message.includes('Unable to validate email address')) {
        notifications.error('Please enter a valid email address.')
      } else if (error.message && error.message.includes('Password should be at least')) {
        notifications.error(error.message) // Use the original message as it's clear
      } else if (error.message && (error.message.includes('confirmation email') ||
                                  error.message.includes('unexpected_failure'))) {
        // For email confirmation errors, show a clear error message
        notifications.error('Registration failed. Please try again later or contact support.')
        return null;
      } else {
        notifications.error(error.message || 'Registration failed. Please try again.')
      }

      return null
    } finally {
      loading.value = false
    }
  }

  // Enhanced signin function with better error handling
  async function signIn(email: string, password: string): Promise<void> {
    loading.value = true
    error.value = null
    try {
      // Authenticate with Supabase
      const { data, error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      // Handle specific error cases
      if (signInError) {
        console.error('Sign in error:', signInError)

        // Check for email not confirmed error
        if (signInError.message && signInError.message.includes('Email not confirmed')) {
          // Show a clear error message
          throw new Error('Your email address has not been confirmed. Please contact support.')
        }

        throw signInError
      }

      if (!data.user) throw new Error('Sign in failed: No user data returned')

      // Set user state
      user.value = data.user

      // Clear session flags
      localStorage.removeItem('profileCompletionPopupShownThisSession')

      // Create personal details record if it doesn't exist (single query)
      const { data: personalDetails, error: personalDetailsError } = await supabase
        .from('personal_details')
        .select('user_id, email_verified')
        .eq('user_id', data.user.id)
        .maybeSingle()

      if (!personalDetails) {
        // Create minimal personal details record
        await supabase
          .from('personal_details')
          .insert({
            user_id: data.user.id,
            email: data.user.email || '',
            profile_state: 'DRAFT',
            profile_completion: 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
      }

      notifications.success('Successfully signed in!')

      // Check if there's a redirect URL in the query parameters
      const route = router.currentRoute.value;
      if (route.query.redirect) {
        // Redirect to the specified URL
        const redirectUrl = route.query.redirect.toString();
        console.log('Redirecting to:', redirectUrl);
        window.location.href = redirectUrl;
      } else {
        // Default redirect to dashboard
        router.push({ name: 'dashboard' });
      }
    } catch (error: any) {
      error.value = error.message

      // Handle specific error messages to make them more user-friendly
      if (error.message && error.message.includes('Invalid login credentials')) {
        notifications.error('Invalid email or password. Please try again.')
      } else if (error.message && error.message.includes('User already registered')) {
        // This error shouldn't happen during sign-in, but if it does, provide a clearer message
        notifications.error('Please use the sign-in form to log in with your existing account.')
      } else {
        notifications.error(error.message || 'Failed to sign in. Please check your credentials.')
      }
    } finally {
      loading.value = false
    }
  }

  // Sign out function
  async function signOut(): Promise<void> {
    try {
      await supabase.auth.signOut()
      user.value = null

      // Clear the profile completion popup session flag
      localStorage.removeItem('profileCompletionPopupShownThisSession')
      localStorage.removeItem('profileCompletionRemindLater')

      // Import and reset the profile store
      const { useProfileStore } = await import('../stores/profile')
      const profileStore = useProfileStore()
      profileStore.reset()

      router.push('/')
      notifications.success('Successfully signed out')
    } catch (error: any) {
      notifications.error(error.message || 'Failed to sign out')
    }
  }

  // Simplified password reset function using Edge Function only
  async function resetPassword(email: string): Promise<void> {
    loading.value = true
    error.value = null
    try {
      // Basic validation
      if (!email) {
        throw new Error('Email is required')
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(email)) {
        throw new Error('Please enter a valid email address')
      }

      console.log('Initiating password reset for:', email)

      // Check if the user exists in our database
      const { data: userData, error: userError } = await supabase
        .from('personal_details')
        .select('first_name')
        .eq('email', email)
        .maybeSingle()

      // Get the first name if available
      const firstName = userData?.first_name

      // Generate a secure token for password reset
      const timestamp = Date.now()
      const randomBytes = crypto.getRandomValues(new Uint8Array(32))
      const randomString = Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('')
      const tokenData = `${email}:${timestamp}:${randomString}`
      const token = btoa(tokenData)

      // Store the token in the database with expiration (24 hours)
      const expiresAt = new Date(timestamp + 24 * 60 * 60 * 1000).toISOString()

      const { error: tokenError } = await supabase
        .from('password_reset_tokens')
        .upsert({
          email,
          token,
          expires_at: expiresAt,
          created_at: new Date().toISOString()
        })

      if (tokenError) {
        console.error('Error storing reset token:', tokenError)
        throw new Error('Failed to generate password reset token')
      }

      // Create the reset link
      const siteUrl = window.location.origin
      const resetLink = `${siteUrl}/reset-password-confirm?token=${encodeURIComponent(token)}`

      // Send email using Edge Function
      const { sendPasswordResetEmail } = await import('../services/emailService')
      const result = await sendPasswordResetEmail(email, resetLink, firstName)

      if (!result.success) {
        throw new Error(result.error || 'Failed to send password reset email')
      }

      console.log('Password reset email sent successfully')

      // Success message
      notifications.success('If an account exists with this email, password reset instructions will be sent.')

    } catch (error: any) {
      console.error('Password reset error:', error)
      error.value = error.message

      // For security, always show success message to prevent email enumeration
      notifications.success('If an account exists with this email, password reset instructions will be sent.')
    } finally {
      loading.value = false
    }
  }

  // Simplified session check
  async function checkSession(): Promise<void> {
    try {
      loading.value = true
      const { data: { session } } = await supabase.auth.getSession()

      if (session) {
        user.value = session.user
      } else {
        user.value = null
      }

      // Set up auth state change listener
      supabase.auth.onAuthStateChange((event, session) => {
        if (session) {
          user.value = session.user
        } else {
          user.value = null
        }
      })
    } catch (err) {
      console.error('Session check error:', err)
    } finally {
      isInitialized.value = true
      loading.value = false
    }
  }

  return {
    user,
    loading,
    error,
    isInitialized,
    isAuthenticated,
    currentUser,
    signUp,
    signIn,
    signOut,
    resetPassword,
    checkSession
  }
})

