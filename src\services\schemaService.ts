/**
 * Schema Service
 * 
 * This service handles loading and managing profile schemas.
 */

import { ref, computed } from 'vue'
import { supabase } from '../lib/supabase'
import { ProfileSchema } from '../types/profile'

// Cache for loaded schemas
const schemaCache = ref<Record<string, ProfileSchema>>({})
const loading = ref(false)
const error = ref<string | null>(null)

/**
 * Load a profile schema from the database or local cache
 * 
 * @param profileType The profile type
 * @returns The profile schema
 */
export async function loadProfileSchema(profileType: string): Promise<ProfileSchema | null> {
  // Check if schema is already in cache
  if (schemaCache.value[profileType]) {
    return schemaCache.value[profileType]
  }
  
  loading.value = true
  error.value = null
  
  try {
    // First try to load from database
    const { data, error: fetchError } = await supabase
      .from('profile_schemas')
      .select('schema')
      .eq('profile_type', profileType)
      .single()
    
    if (fetchError) {
      console.log(`Schema not found in database for ${profileType}, using local schema`)
      // If not in database, try to load from local schemas
      const schema = await loadLocalSchema(profileType)
      
      if (schema) {
        schemaCache.value[profileType] = schema
        return schema
      }
      
      console.error(`No schema found for profile type: ${profileType}`)
      error.value = `No schema found for profile type: ${profileType}`
      return null
    }
    
    if (data?.schema) {
      const schema = data.schema as ProfileSchema
      schemaCache.value[profileType] = schema
      return schema
    }
    
    return null
  } catch (err) {
    console.error('Error loading profile schema:', err)
    error.value = 'Error loading profile schema'
    return null
  } finally {
    loading.value = false
  }
}

/**
 * Load a schema from local files
 * 
 * @param profileType The profile type
 * @returns The profile schema
 */
async function loadLocalSchema(profileType: string): Promise<ProfileSchema | null> {
  try {
    // This would be replaced with actual imports of JSON schema files
    // For now, we'll return a simple schema for testing
    return {
      profileType,
      sections: [
        {
          title: 'Basic Information',
          icon: 'person',
          description: 'Basic profile information',
          questions: [
            {
              id: 'bio',
              type: 'textarea',
              label: 'Bio',
              required: false
            }
          ]
        }
      ]
    }
  } catch (err) {
    console.error(`Error loading local schema for ${profileType}:`, err)
    return null
  }
}

/**
 * Use the schema service
 * 
 * @returns The schema service
 */
export function useSchemaService() {
  return {
    loadProfileSchema,
    loading: computed(() => loading.value),
    error: computed(() => error.value)
  }
}
