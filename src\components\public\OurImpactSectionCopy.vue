<template>
  <section class="our-impact-section">
    <div class="background-overlay"></div>
    <div class="container q-mx-auto q-px-md">
      <div class="row">
        <div class="col-1" />
        <div class="col-10">
          <div class="text-center">
            <div class="section-header">
              <h2 class="section-title">Our Innovation Impact</h2>
              <p class="section-description">
                Measuring success through meaningful connections, growth, and innovation achievements across our ecosystem.
              </p>
            </div>
          </div>

          <!-- Impact Metrics Grid -->
          <div class="impact-metrics-grid">
            <div 
              v-for="(metric, index) in impactMetrics" 
              :key="index" 
              class="impact-metric-card"
              :style="{ animationDelay: `${index * 0.2}s` }"
            >
              <div class="metric-content">
                <div class="metric-icon-container">
                  <q-icon :name="metric.icon" size="3rem" color="white" class="metric-icon" />
                </div>
                <div class="metric-number" ref="counterRefs">{{ displayValues[index] }}</div>
                <div class="metric-label">{{ metric.label }}</div>
                <div class="metric-description">{{ metric.description }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-1" />
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const displayValues = ref([0, 0, 0, 0])
const countersAnimated = ref(false)

const impactMetrics = ref([
  {
    icon: 'business',
    value: 250,
    label: 'Startups Launched',
    description: 'Successful ventures created',
    color: 'primary'
  },
  {
    icon: 'attach_money',
    value: 50,
    label: 'Million Raised',
    description: 'In funding secured',
    color: 'secondary'
  },
  {
    icon: 'people',
    value: 1500,
    label: 'Active Members',
    description: 'Growing community',
    color: 'accent'
  },
  {
    icon: 'trending_up',
    value: 95,
    label: 'Success Rate',
    description: 'Project completion',
    color: 'positive'
  }
])

const animateCounter = (index: number, target: number) => {
  const duration = 2000
  const steps = 60
  const increment = target / steps
  let current = 0
  
  const timer = setInterval(() => {
    current += increment
    if (current >= target) {
      displayValues.value[index] = target
      clearInterval(timer)
    } else {
      displayValues.value[index] = Math.floor(current)
    }
  }, duration / steps)
}

onMounted(() => {
  setTimeout(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && !countersAnimated.value) {
            countersAnimated.value = true
            
            impactMetrics.value.forEach((metric, index) => {
              setTimeout(() => {
                animateCounter(index, metric.value)
              }, index * 200)
            })
            
            observer.unobserve(entry.target)
          }
        })
      },
      {
        threshold: 0.3,
        rootMargin: '0px 0px -50px 0px'
      }
    )

    const impactSection = document.querySelector('.our-impact-section')
    if (impactSection) {
      observer.observe(impactSection)
    }
  }, 100)
})
</script>

<style scoped>
.our-impact-section {
  position: relative;
  padding: 80px 0;
  background-image: url('@/assets/hero/business-concept-business-people-shaking-hands-showing-mutual-agreement-betweent-their-companies-firms.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  overflow: hidden;
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(67, 56, 202, 0.85) 0%, 
    rgba(139, 92, 246, 0.75) 50%,
    rgba(67, 56, 202, 0.85) 100%);
  z-index: 1;
}

.container {
  position: relative;
  z-index: 2;
}

.section-header {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 40px;
  margin-bottom: 60px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 20px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.section-description {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.impact-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-top: 40px;
}

.impact-metric-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: translateY(30px);
  animation: slideUp 0.6s ease forwards;
}

.impact-metric-card:hover {
  transform: translateY(-10px);
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
}

.metric-content {
  text-align: center;
  color: white;
}

.metric-icon-container {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  backdrop-filter: blur(10px);
}

.metric-number {
  font-size: 3rem;
  font-weight: 700;
  color: #A78BFA;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.metric-label {
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  margin-bottom: 8px;
}

.metric-description {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  text-transform: uppercase;
  letter-spacing: 1px;
}

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .our-impact-section {
    padding: 60px 0;
    background-attachment: scroll;
  }

  .section-header {
    padding: 30px 20px;
    margin-bottom: 40px;
  }

  .section-title {
    font-size: 2rem;
  }

  .section-description {
    font-size: 1rem;
  }

  .impact-metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
  }

  .impact-metric-card {
    padding: 25px;
    /* Ensure visibility on mobile */
    opacity: 1 !important;
    transform: translateY(0) !important;
    animation: none;
  }

  .metric-number {
    font-size: 2.5rem;
  }

  .metric-label {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .section-header {
    padding: 20px 15px;
  }

  .section-title {
    font-size: 1.8rem;
  }

  .impact-metrics-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .impact-metric-card {
    padding: 20px;
  }

  .metric-number {
    font-size: 2rem;
  }
}
</style>
