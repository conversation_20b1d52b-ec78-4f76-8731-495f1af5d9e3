<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome Email Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #0D8A3E;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0a6b31;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        input[type="email"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 250px;
            margin: 5px;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>✅ Welcome Email Fix Test</h1>
    
    <div class="test-section">
        <h2>🔧 Fix Summary</h2>
        <div class="success">
            <h3>Issues Fixed:</h3>
            <ul>
                <li>✅ <strong>sendEmailRequest function error</strong> - Fixed undefined function reference in emailService.ts</li>
                <li>✅ <strong>Email sender address</strong> - <NAME_EMAIL></li>
                <li>✅ <strong>Edge function deployment</strong> - Deployed updated send-email-smilefactory function</li>
                <li>✅ <strong>Duplicate email prevention</strong> - Disabled redundant auth-webhook function</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 Test Welcome Email Function</h2>
        <p>Test the fixed welcome email functionality directly.</p>
        
        <div>
            <label for="testEmail">Test Email Address:</label><br>
            <input type="email" id="testEmail" placeholder="Enter your email address" value="<EMAIL>">
        </div>
        
        <div>
            <label for="testName">Test Name (optional):</label><br>
            <input type="text" id="testName" placeholder="Enter test name" value="Test User">
        </div>
        
        <button class="test-button" onclick="testWelcomeEmailDirect()" id="directTestBtn">Test Direct Email Function</button>
        
        <div id="direct-test-results"></div>
    </div>

    <div class="test-section">
        <h2>📋 Registration Flow Test</h2>
        <div class="info">
            <h3>Manual Testing Steps:</h3>
            <ol>
                <li>Open the application: <a href="http://localhost:5174" target="_blank">http://localhost:5174</a></li>
                <li>Click "Sign Up" to create a new account</li>
                <li>Select a category (e.g., Innovator)</li>
                <li>Fill in email and password</li>
                <li>Complete the registration</li>
                <li>Check your email inbox for the welcome email</li>
                <li>Verify the email comes from: <strong><EMAIL></strong></li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h2>🔍 Technical Details</h2>
        <div class="code-block">
            <strong>Fixed Function Reference:</strong><br>
            // Before (BROKEN):<br>
            const result = await sendEmailRequest(emailRequest)<br><br>
            
            // After (FIXED):<br>
            const result = await sendEmail(emailRequest)<br><br>
            
            <strong>Updated Email Configuration:</strong><br>
            - Sender: <EMAIL><br>
            - API Key: EMAIL_API_KEY environment variable<br>
            - Function: send-email-smilefactory (version 2)<br>
            - Service: Resend API
        </div>
    </div>

    <div class="test-section">
        <h2>🚀 Next Steps</h2>
        <div class="warning">
            <h3>To complete the setup:</h3>
            <ol>
                <li>Ensure EMAIL_API_KEY is set in Supabase environment variables</li>
                <li>Verify <EMAIL> is configured in Resend</li>
                <li>Test the complete registration flow with a real email</li>
                <li>Monitor Supabase function logs for any errors</li>
            </ol>
        </div>
    </div>

    <script>
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `result ${type}`;
            result.innerHTML = message;
            container.appendChild(result);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        async function testWelcomeEmailDirect() {
            const emailInput = document.getElementById('testEmail');
            const nameInput = document.getElementById('testName');
            const button = document.getElementById('directTestBtn');
            
            const email = emailInput.value.trim();
            const name = nameInput.value.trim();
            
            if (!email) {
                addResult('direct-test-results', '❌ Please enter an email address', 'error');
                return;
            }
            
            clearResults('direct-test-results');
            button.disabled = true;
            button.textContent = 'Testing...';
            
            try {
                addResult('direct-test-results', `📧 Testing welcome email to: ${email}`, 'info');
                
                // Test the Supabase function directly
                const response = await fetch('https://dpicnvisvxpmgjtbeicf.supabase.co/functions/v1/send-email-fixed', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc'
                    },
                    body: JSON.stringify({
                        type: 'welcome',
                        data: {
                            to: email,
                            firstName: name || undefined
                        }
                    })
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    addResult('direct-test-results', '✅ Welcome email function test successful!', 'success');
                    addResult('direct-test-results', `📬 Message: ${result.message}`, 'success');
                    addResult('direct-test-results', '📧 Check your email inbox (including spam folder)', 'info');
                } else {
                    addResult('direct-test-results', `❌ Function test failed: ${result.error || 'Unknown error'}`, 'error');
                    addResult('direct-test-results', `📊 Response: ${JSON.stringify(result)}`, 'info');
                }
                
            } catch (error) {
                addResult('direct-test-results', `❌ Network error: ${error.message}`, 'error');
                addResult('direct-test-results', '💡 Note: This test requires the function to be deployed and accessible', 'warning');
            } finally {
                button.disabled = false;
                button.textContent = 'Test Direct Email Function';
            }
        }

        // Add some initial information
        window.onload = function() {
            addResult('direct-test-results', '✅ Welcome email fix has been deployed successfully!', 'success');
            addResult('direct-test-results', '🧪 Ready to test the fixed email functionality', 'info');
        };
    </script>
</body>
</html>
