<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Functionality Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #0D8A3E;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0a6b31;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        input[type="email"] {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 250px;
            margin: 5px;
        }
        .loading {
            opacity: 0.6;
        }
    </style>
</head>
<body>
    <h1>Email Functionality Test</h1>
    
    <div class="test-section">
        <h2>Email Configuration Summary</h2>
        <div class="info">
            <h3>Current Configuration:</h3>
            <ul>
                <li><strong>Email Function:</strong> send-email-smilefactory</li>
                <li><strong>API Key:</strong> EMAIL_API_KEY environment variable</li>
                <li><strong>Sender Email:</strong> <EMAIL></li>
                <li><strong>Sender Name:</strong> SmileFactory</li>
                <li><strong>Email Service:</strong> Resend API</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>Test Email Sending</h2>
        <p>Test the welcome email functionality with the updated configuration.</p>
        
        <div>
            <label for="testEmail">Test Email Address:</label><br>
            <input type="email" id="testEmail" placeholder="Enter your email address" value="<EMAIL>">
        </div>
        
        <div>
            <label for="testName">Test Name (optional):</label><br>
            <input type="text" id="testName" placeholder="Enter test name" value="Test User">
        </div>
        
        <button class="test-button" onclick="testWelcomeEmail()" id="emailTestBtn">Send Test Welcome Email</button>
        
        <div id="email-results"></div>
    </div>

    <div class="test-section">
        <h2>Registration Flow Test</h2>
        <div class="warning">
            <h3>Manual Testing Required:</h3>
            <p>To fully test the email functionality:</p>
            <ol>
                <li>Open the application: <a href="http://localhost:5174" target="_blank">http://localhost:5174</a></li>
                <li>Sign up with a real email address</li>
                <li>Check your email inbox for the welcome email</li>
                <li>Verify the email comes from: <EMAIL></li>
                <li>Verify the email content is properly formatted</li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h2>Troubleshooting</h2>
        <div class="info">
            <h3>If emails are not being sent:</h3>
            <ol>
                <li>Check that EMAIL_API_KEY is set in Supabase environment variables</li>
                <li>Verify the send-email-smilefactory function is deployed</li>
                <li>Check Supabase function logs for errors</li>
                <li>Ensure <EMAIL> is verified in Resend</li>
                <li>Check spam/junk folder in email client</li>
            </ol>
        </div>
    </div>

    <script>
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const result = document.createElement('div');
            result.className = `result ${type}`;
            result.innerHTML = message;
            container.appendChild(result);
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        async function testWelcomeEmail() {
            const emailInput = document.getElementById('testEmail');
            const nameInput = document.getElementById('testName');
            const button = document.getElementById('emailTestBtn');
            
            const email = emailInput.value.trim();
            const name = nameInput.value.trim();
            
            if (!email) {
                addResult('email-results', '✗ Please enter an email address', 'error');
                return;
            }
            
            // Basic email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                addResult('email-results', '✗ Please enter a valid email address', 'error');
                return;
            }
            
            clearResults('email-results');
            button.disabled = true;
            button.textContent = 'Sending...';
            
            try {
                addResult('email-results', `📧 Attempting to send welcome email to: ${email}`, 'info');
                
                // This would normally call the actual email service
                // For testing purposes, we'll simulate the API call
                const response = await simulateEmailAPI(email, name);
                
                if (response.success) {
                    addResult('email-results', '✓ Email API call successful', 'success');
                    addResult('email-results', `✓ Message: ${response.message}`, 'success');
                    addResult('email-results', '📬 Check your email inbox (including spam folder)', 'info');
                } else {
                    addResult('email-results', `✗ Email API call failed: ${response.error}`, 'error');
                }
                
            } catch (error) {
                addResult('email-results', `✗ Error: ${error.message}`, 'error');
            } finally {
                button.disabled = false;
                button.textContent = 'Send Test Welcome Email';
            }
        }

        async function simulateEmailAPI(email, name) {
            // Simulate API delay
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Simulate the email service response
            // In a real test, this would call the actual Supabase function
            return {
                success: true,
                message: `Welcome email would be sent to ${email} from <EMAIL>`
            };
        }

        // Add some initial information
        window.onload = function() {
            addResult('email-results', 'Ready to test email functionality', 'info');
            addResult('email-results', 'Note: This is a simulation. For real testing, use the registration flow.', 'warning');
        };
    </script>
</body>
</html>
