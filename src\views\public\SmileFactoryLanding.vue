<template>
  <div class="smile-factory-landing">
    <!-- Navigation -->
    <nav class="navigation">
      <div class="nav-container">
        <router-link to="/" class="logo">
          <img src="@/assets/logo/ZBFH_SmileFactory Logo_1.png" alt="Smile Factory" class="logo-img" />
        </router-link>
        
        <!-- Desktop Navigation -->
        <div class="nav-buttons desktop-nav">
          <button class="btn-secondary" @click="handleSignIn">Sign In</button>
          <button class="btn-primary" @click="handleJoinNow">Join Now</button>
        </div>
        
        <!-- Mobile Navigation -->
        <button class="mobile-menu-button mobile-nav" @click="toggleMobileMenu">
          <span class="sr-only">Open main menu</span>
          <svg class="hamburger-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-content">
        <div class="startup-badge">
          <svg class="badge-icon" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
          </svg>
          25+ Startups Launched
        </div>
        
        <h1 class="hero-title">
          Bringing you a vibrant ecosystem<br>
          that fosters innovation
        </h1>
        
        <button class="btn-large">
          Join the Community Today
          <svg class="arrow-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14m-7-7l7 7-7 7"/>
          </svg>
        </button>
      </div>
    </section>

    <!-- Innovation Ecosystem Section -->
    <section class="innovation-ecosystem-section">
      <div class="container">
        <h2 class="section-heading">Innovation Ecosystem</h2>
        <p class="section-description">
          Smile-Factory is bringing you a vibrant ecosystem that fosters innovation. By combining the power of a state-of-the-art physical hub with a cutting-edge virtual community, we create a unique environment for collaboration between all players in the innovation ecosystem.
        </p>

        <div class="ecosystem-cards">
          <div class="ecosystem-card glass-card" @mouseenter="handleCardHover('connect')" @mouseleave="handleCardLeave">
            <div class="card-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                <path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.5-12h-4l-2.5 12H16v6h4zM12.5 11.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5S11 9.17 11 10s.67 1.5 1.5 1.5zM5.5 6c1.11 0 2-.89 2-2s-.89-2-2-2-2 .89-2 2 .89 2 2 2zm2 16v-7H9V9H1v6h1.5v7h5z"/>
              </svg>
            </div>
            <h3>Connect</h3>
            <p>Connect with other innovators, mentors, and resources in the ecosystem.</p>
            <button class="card-button">
              <span>Community Feed</span>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14m-7-7l7 7-7 7"/>
              </svg>
            </button>
          </div>

          <div class="ecosystem-card glass-card" @mouseenter="handleCardHover('network')" @mouseleave="handleCardLeave">
            <div class="card-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
              </svg>
            </div>
            <h3>Network</h3>
            <p>Build valuable relationships with industry leaders and potential partners.</p>
            <button class="card-button">
              <span>Profiles Directory</span>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14m-7-7l7 7-7 7"/>
              </svg>
            </button>
          </div>

          <div class="ecosystem-card glass-card" @mouseenter="handleCardHover('grow')" @mouseleave="handleCardLeave">
            <div class="card-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z"/>
              </svg>
            </div>
            <h3>Grow</h3>
            <p>Access resources and support to help your innovation thrive.</p>
            <button class="card-button">
              <span>Marketplace</span>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14m-7-7l7 7-7 7"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Platform Features Carousel Section -->
    <section class="platform-features-section">
      <div class="container">
        <div class="features-header">
          <h2 class="section-heading">Platform Features</h2>
          <p class="section-description">
            Explore the powerful tools and features that make our innovation ecosystem unique and effective.
          </p>
        </div>

        <div class="features-carousel">
          <div class="carousel-container" ref="carouselContainer">
            <div class="carousel-track" :style="{ transform: `translateX(-${currentSlide * 100}%)` }">
              <div class="feature-slide" v-for="(feature, index) in platformFeatures" :key="index">
                <div class="feature-card glass-card">
                  <div class="feature-icon floating-icon">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                      <path :d="feature.iconPath"/>
                    </svg>
                  </div>
                  <h3>{{ feature.title }}</h3>
                  <p>{{ feature.description }}</p>
                  <div class="feature-list">
                    <div v-for="item in feature.features" :key="item" class="feature-item">
                      <div class="feature-check">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                        </svg>
                      </div>
                      <span>{{ item }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="carousel-controls">
            <button class="carousel-btn prev" @click="prevSlide" :disabled="currentSlide === 0">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 18l-6-6 6-6"/>
              </svg>
            </button>
            <div class="carousel-indicators">
              <button
                v-for="(_, index) in platformFeatures"
                :key="index"
                class="indicator"
                :class="{ active: currentSlide === index }"
                @click="goToSlide(index)"
              ></button>
            </div>
            <button class="carousel-btn next" @click="nextSlide" :disabled="currentSlide === platformFeatures.length - 1">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 18l6-6-6-6"/>
              </svg>
            </button>
          </div>
        </div>

        <button class="btn-large explore-features-btn">
          Explore All Features
        </button>
      </div>
    </section>

    <!-- Our Impact Section -->
    <section class="our-impact-section">
      <div class="container">
        <div class="impact-header">
          <h2 class="section-heading">Our Impact</h2>
          <p class="section-description">
            See the measurable difference we're making in Zimbabwe's innovation ecosystem through our growing community and initiatives.
          </p>
        </div>

        <div class="impact-stats">
          <div class="stat-card glass-card" v-for="stat in impactStats" :key="stat.title">
            <div class="stat-icon">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
                <path :d="stat.iconPath"/>
              </svg>
            </div>
            <div class="stat-content">
              <div class="stat-number">
                <span class="number">{{ stat.number }}</span>
                <span class="suffix">{{ stat.suffix }}</span>
              </div>
              <h3>{{ stat.title }}</h3>
              <p>{{ stat.description }}</p>
            </div>
          </div>
        </div>

        <button class="btn-large get-started-btn">
          <span>Get Started Today</span>
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14m-7-7l7 7-7 7"/>
          </svg>
        </button>
      </div>
    </section>

    <!-- News & Updates Section -->
    <section class="news-updates-section">
      <div class="container">
        <h2 class="section-heading">News & Updates</h2>
        <div class="news-header">
          <div class="featured-badge">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            <span>Featured News & Updates</span>
          </div>
        </div>

        <div class="news-articles">
          <article class="news-article glass-card" v-for="article in newsArticles" :key="article.title">
            <div class="article-badge">Featured</div>
            <div class="article-header">
              <div class="article-category">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                  <path :d="article.categoryIcon"/>
                </svg>
                <span>{{ article.category }}</span>
              </div>
              <h3>{{ article.title }}</h3>
              <time>{{ article.date }}</time>
            </div>
            <p>{{ article.excerpt }}</p>
            <button class="read-more-btn">
              <span>Read More</span>
            </button>
          </article>
        </div>

        <a href="/innovation-community?tab=blog" class="view-all-link">
          <span>View All Stories</span>
        </a>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="footer-content">
        <div class="footer-main">
          <div class="footer-brand">
            <img src="@/assets/logo/ZBFH_SmileFactory Logo_1.png" alt="Smile Factory" class="footer-logo" />
            <p class="footer-description">
              Connecting all stakeholders in the innovation ecosystem to create a powerful network of collaboration and growth.
            </p>
            <div class="footer-contact">
              <svg class="email-icon" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.89 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
              </svg>
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </div>
          </div>
          
          <div class="footer-links">
            <div class="footer-column">
              <h3>Platform</h3>
              <ul>
                <li><a href="#who-we-are">Who We Are</a></li>
                <li><a href="#our-mandate">Our Mandate</a></li>
                <li><a href="#the-community">The Community</a></li>
                <li><a href="#success-stories">Success Stories</a></li>
                <li><a href="#how-it-works">How It Works</a></li>
              </ul>
            </div>
            
            <div class="footer-column">
              <h3>Join As</h3>
              <ul>
                <li><a href="/register?type=innovator">Entrepreneur</a></li>
                <li><a href="/register?type=mentor">Mentor</a></li>
                <li><a href="/register?type=student">Student</a></li>
                <li><a href="/register?type=funder">Investor</a></li>
                <li><a href="/register?type=academic">Institution</a></li>
                <li><a href="/register?type=expert">Expert</a></li>
              </ul>
            </div>
            
            <div class="footer-column">
              <h3>Resources</h3>
              <ul>
                <li><a href="/resources/getting-started">Getting Started Guide</a></li>
                <li><a href="/resources/best-practices">Best Practices</a></li>
                <li><a href="/resources/guidelines">Community Guidelines</a></li>
                <li><a href="/help">Help Center</a></li>
                <li><a href="/blog">Blog</a></li>
              </ul>
            </div>
          </div>
        </div>
        
        <div class="newsletter-section">
          <h3>Stay Connected</h3>
          <p>Get the latest updates on platform features, success stories, and innovation trends.</p>
          <div class="newsletter-form">
            <input type="email" placeholder="Enter your email" class="email-input" v-model="email" />
            <button class="btn-primary">Subscribe</button>
          </div>
        </div>
      </div>
      
      <div class="footer-bottom">
        <div class="footer-bottom-content">
          <p>&copy; 2025 Smile Factory</p>
          <div class="legal-links">
            <a href="/privacy">Privacy Policy</a>
            <a href="/terms">Terms of Service</a>
            <a href="/cookies">Cookie Policy</a>
            <a href="/data-protection">Data Protection</a>
          </div>
        </div>
        
        <div class="social-links">
          <span>Follow us:</span>
          <a href="https://linkedin.com/company/smile-factory" aria-label="Follow us on LinkedIn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
            </svg>
          </a>
          <a href="https://twitter.com/SmileFactoryHQ" aria-label="Follow us on Twitter">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
            </svg>
          </a>
          <a href="https://youtube.com/SmileFactoryPlatform" aria-label="Follow us on YouTube">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
            </svg>
          </a>
        </div>
      </div>
    </footer>

    <!-- Auth Dialogs -->
    <UnifiedAuthDialogs />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useUnifiedAuth } from '@/services/unifiedAuthService'
import UnifiedAuthDialogs from '@/components/auth/UnifiedAuthDialogs.vue'

// Services
const { openSignUpDialog, openSignInDialog } = useUnifiedAuth()




// Reactive data
const selectedCard = ref('connect')
const email = ref('')
const mobileMenuOpen = ref(false)
const currentSlide = ref(0)
const carouselContainer = ref(null)

// Platform Features Data
const platformFeatures = ref([
  {
    title: 'Community Networking',
    description: 'Connect with entrepreneurs, investors, mentors, and industry experts in a vibrant community.',
    iconPath: 'M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.5-12h-4l-2.5 12H16v6h4zM12.5 11.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5S11 9.17 11 10s.67 1.5 1.5 1.5zM5.5 6c1.11 0 2-.89 2-2s-.89-2-2-2-2 .89-2 2 .89 2 2 2zm2 16v-7H9V9H1v6h1.5v7h5z',
    features: ['Advanced profile matching', 'Direct messaging system', 'Interest-based groups', 'Virtual networking events']
  },
  {
    title: 'Learning Resources',
    description: 'Access a wealth of educational content and training opportunities to grow your skills.',
    iconPath: 'M5 13.18v4L12 21l7-3.82v-4L12 17l-7-3.82zM12 3L1 9l11 6 9-4.91V17h2V9L12 3z',
    features: ['Expert-led workshops', 'On-demand video courses', 'Downloadable resources', 'Personalized learning paths']
  },
  {
    title: 'Collaboration Tools',
    description: 'Find partners and collaborate effectively on innovative projects and initiatives.',
    iconPath: 'M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z',
    features: ['Project management tools', 'Shared document workspaces', 'Virtual meeting rooms', 'Co-creation boards']
  },
  {
    title: 'Growth Opportunities',
    description: 'Discover funding, mentorship, and resources to accelerate your innovation journey.',
    iconPath: 'M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z',
    features: ['Investor matching', 'Grant application support', 'Pitch deck reviews', 'Growth strategy consulting']
  },
  {
    title: 'Events & Programs',
    description: 'Participate in events, workshops, and programs designed to foster innovation.',
    iconPath: 'M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z',
    features: ['Hackathons & challenges', 'Networking mixers', 'Industry showcases', 'Accelerator programs']
  },
  {
    title: 'Physical Hub Access',
    description: 'Utilize our state-of-the-art physical facilities for meetings, work, and events.',
    iconPath: 'M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z',
    features: ['Co-working spaces', 'Meeting rooms', 'Event venues', 'Innovation labs']
  }
])

// Impact Stats Data
const impactStats = ref([
  {
    number: '500',
    suffix: '+',
    title: 'Community Members',
    description: 'Innovators, entrepreneurs, and industry experts connected',
    iconPath: 'M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.5-12h-4l-2.5 12H16v6h4zM12.5 11.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5S11 9.17 11 10s.67 1.5 1.5 1.5zM5.5 6c1.11 0 2-.89 2-2s-.89-2-2-2-2 .89-2 2 .89 2 2 2zm2 16v-7H9V9H1v6h1.5v7h5z'
  },
  {
    number: '150',
    suffix: '+',
    title: 'Innovation Projects',
    description: 'Ideas transformed into viable business solutions',
    iconPath: 'M9 21c0 .5.4 1 1 1h4c.6 0 1-.5 1-1v-1H9v1zm3-19C8.1 2 5 5.1 5 9c0 2.4 1.2 4.5 3 5.7V17c0 .5.4 1 1 1h6c.6 0 1-.5 1-1v-2.3c1.8-1.3 3-3.4 3-5.7 0-3.9-3.1-7-7-7z'
  },
  {
    number: '75',
    suffix: '+',
    title: 'Partnerships Formed',
    description: 'Strategic connections between innovators and investors',
    iconPath: 'M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 ********** 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 **********.03.74-.25 1.02l-2.2 2.2z'
  },
  {
    number: '25',
    suffix: 'M+',
    title: 'Funding Facilitated',
    description: 'USD in investment opportunities created',
    iconPath: 'M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6z'
  },
  {
    number: '200',
    suffix: '+',
    title: 'Training Sessions',
    description: 'Workshops and mentorship programs delivered',
    iconPath: 'M5 13.18v4L12 21l7-3.82v-4L12 17l-7-3.82zM12 3L1 9l11 6 9-4.91V17h2V9L12 3z'
  },
  {
    number: '50',
    suffix: '+',
    title: 'Startups Launched',
    description: 'New businesses successfully established',
    iconPath: 'M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z'
  }
])

// News Articles Data
const newsArticles = ref([
  {
    title: 'AI and Machine Learning: Transforming African Industries',
    category: 'ai',
    categoryIcon: 'M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z',
    date: 'August 9, 2025',
    excerpt: 'Artificial Intelligence and Machine Learning are no longer futuristic concepts in Africa. They are actively transforming industries and creating new opportunities for innovation and growth.'
  },
  {
    title: 'Building Sustainable Startups: Lessons from African Entrepreneurs',
    category: 'entrepreneurship',
    categoryIcon: 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z',
    date: 'August 8, 2025',
    excerpt: 'African entrepreneurs are redefining what it means to build sustainable businesses. This article explores key strategies and insights from successful startup founders across the continent.'
  },
  {
    title: 'The Future of Innovation in Zimbabwe: A Digital Renaissance',
    category: 'innovation',
    categoryIcon: 'M9 21c0 .5.4 1 1 1h4c.6 0 1-.5 1-1v-1H9v1zm3-19C8.1 2 5 5.1 5 9c0 2.4 1.2 4.5 3 5.7V17c0 .5.4 1 1 1h6c.6 0 1-.5 1-1v-2.3c1.8-1.3 3-3.4 3-5.7 0-3.9-3.1-7-7-7z',
    date: 'August 7, 2025',
    excerpt: 'Zimbabwe is experiencing a remarkable digital transformation that is reshaping the innovation landscape across the country. From fintech startups to agritech solutions, local entrepreneurs are leveraging technology to solve real-world problems.'
  }
])

// Methods
const selectCard = (card: string) => {
  selectedCard.value = card
}

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
}

const handleSignIn = () => {
  openSignInDialog()
}

const handleJoinNow = () => {
  openSignUpDialog()
}

const handleCardHover = (cardType: string) => {
  // Add hover effects for ecosystem cards
}

const handleCardLeave = () => {
  // Remove hover effects
}

// Carousel methods
const nextSlide = () => {
  if (currentSlide.value < platformFeatures.value.length - 1) {
    currentSlide.value++
  }
}

const prevSlide = () => {
  if (currentSlide.value > 0) {
    currentSlide.value--
  }
}

const goToSlide = (index: number) => {
  currentSlide.value = index
}
</script>

<style scoped>
/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Sora:wght@400;600&family=Plus+Jakarta+Sans:wght@400;500;600&display=swap');

/* Global Styles */
.smile-factory-landing {
  font-family: 'Plus Jakarta Sans', sans-serif;
  line-height: 1.6;
  color: #000;
  overflow-x: hidden;
}

/* Navigation Styles */
.navigation {
  position: fixed;
  top: 80px;
  width: 100%;
  z-index: 50;
  transition: all 100ms ease;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 16px 0;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.logo-img {
  height: 40px;
  width: auto;
}

.nav-buttons {
  display: flex;
  gap: 16px;
  align-items: center;
}

.mobile-menu-button {
  display: none;
  padding: 8px;
  border-radius: 6px;
  background: transparent;
  border: none;
  cursor: pointer;
  color: #374151;
}

.hamburger-icon {
  width: 24px;
  height: 24px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Button Styles */
.btn-primary {
  background-color: rgb(131, 186, 38);
  color: white;
  padding: 8px 24px;
  border-radius: 9999px;
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background-color: rgb(111, 156, 28);
  transform: translateY(-1px);
}

.btn-secondary {
  background: transparent;
  color: rgb(131, 186, 38);
  padding: 8px 16px;
  border: none;
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 6px;
}

.btn-secondary:hover {
  color: rgb(111, 156, 28);
  background: rgba(131, 186, 38, 0.1);
}

.btn-large {
  background: transparent;
  color: white;
  padding: 16px 32px;
  border: 2px solid white;
  border-radius: 9999px;
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 18px;
  font-weight: 400;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.btn-large:hover {
  background: white;
  color: rgb(131, 186, 38);
  transform: translateY(-2px);
}

.arrow-icon {
  transition: transform 0.3s ease;
}

.btn-large:hover .arrow-icon {
  transform: translateX(4px);
}

/* Hero Section */
.hero-section {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(0, 106, 57, 0.95) 0%, rgba(131, 186, 38, 0.9) 100%),
              url('@/assets/hero/business-concept-business-people-shaking-hands-showing-mutual-agreement-betweent-their-companies-firms.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  color: white;
  text-align: center;
  position: relative;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 24px;
  z-index: 2;
}

.startup-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 24px;
  font-size: 14px;
  color: white;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.badge-icon {
  width: 16px;
  height: 16px;
}

.hero-title {
  font-family: 'Sora', sans-serif;
  font-size: 60px;
  font-weight: 600;
  line-height: 60px;
  color: white;
  margin-bottom: 32px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Content Sections */
.content-section {
  padding: 48px 0;
  display: flex;
  align-items: center;
  min-height: 400px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  text-align: center;
}

.section-heading {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 18px;
  font-weight: 600;
  line-height: 28px;
  margin-bottom: 16px;
}

.who-we-are {
  color: #374151;
}

.our-mandate {
  color: rgb(131, 186, 38);
}

.the-goal {
  color: #374151;
}

.section-description {
  font-family: 'Sora', sans-serif;
  font-size: 48px;
  font-weight: 600;
  line-height: 48px;
  color: rgb(131, 186, 38);
  max-width: 800px;
  margin: 0 auto;
}

/* Goal Section */
.goal-section {
  padding: 48px 0;
  background: linear-gradient(135deg, rgba(0, 106, 57, 0.95) 0%, rgba(131, 186, 38, 0.9) 100%),
              url('@/assets/hero/group-afro-americans-working-together%20(1).jpg');
  background-size: cover;
  background-position: center;
  color: white;
  text-align: center;
}

.goal-description {
  font-family: 'Sora', sans-serif;
  font-size: 48px;
  font-weight: 600;
  line-height: 48px;
  color: rgb(0, 106, 57);
  max-width: 800px;
  margin: 0 auto 40px;
}

.goal-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin: 40px 0;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.goal-card {
  background: rgba(0, 0, 0, 0.5);
  border-radius: 12px;
  padding: 32px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.goal-card:hover {
  transform: translateY(-4px);
  background: rgba(0, 0, 0, 0.7);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.goal-card h3 {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 24px;
  font-weight: 600;
  line-height: 32px;
  color: white;
  margin-bottom: 0;
}

.card-content {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 32px;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  transition: opacity 0.3s ease;
}

.goal-card:hover .card-content {
  opacity: 1;
}

.card-content h3 {
  margin-bottom: 16px;
}

.card-content p {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 26px;
  color: white;
  margin: 0;
}

/* Community Section */
.community-section {
  padding: 48px 0;
  background: white;
  text-align: center;
}

.community-heading {
  color: #374151;
  margin-bottom: 24px;
}

.community-subheading {
  font-family: 'Sora', sans-serif;
  font-size: 48px;
  font-weight: 600;
  line-height: 48px;
  color: rgb(131, 186, 38);
  max-width: 900px;
  margin: 0 auto 24px;
}

.community-description {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 18px;
  font-weight: 400;
  line-height: 28px;
  color: #374151;
  max-width: 800px;
  margin: 0 auto 40px;
}

/* Footer */
.footer {
  background-color: #1f2937;
  color: #d1d5db;
  padding: 48px 0 24px;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.footer-main {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 48px;
  margin-bottom: 48px;
}

.footer-brand {
  max-width: 400px;
}

.footer-logo {
  height: 40px;
  width: auto;
  margin-bottom: 16px;
  filter: brightness(0) invert(1);
}

.footer-description {
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 16px;
  color: #9ca3af;
}

.footer-contact {
  display: flex;
  align-items: center;
  gap: 8px;
}

.email-icon {
  width: 16px;
  height: 16px;
  color: rgb(131, 186, 38);
}

.footer-contact a {
  color: #d1d5db;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-contact a:hover {
  color: rgb(131, 186, 38);
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
}

.footer-column h3 {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  color: white;
  margin-bottom: 16px;
}

.footer-column ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-column li {
  margin-bottom: 8px;
}

.footer-column a {
  color: #9ca3af;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-column a:hover {
  color: rgb(131, 186, 38);
}

.newsletter-section {
  background: #374151;
  padding: 32px;
  border-radius: 12px;
  margin-bottom: 32px;
  text-align: center;
}

.newsletter-section h3 {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  color: white;
  margin-bottom: 8px;
}

.newsletter-section p {
  color: #9ca3af;
  margin-bottom: 24px;
  font-size: 14px;
}

.newsletter-form {
  display: flex;
  gap: 12px;
  max-width: 400px;
  margin: 0 auto;
}

.email-input {
  flex: 1;
  padding: 8px 16px;
  border: 1px solid #4b5563;
  border-radius: 6px;
  background: #1f2937;
  color: white;
  font-size: 14px;
}

.email-input::placeholder {
  color: #9ca3af;
}

.email-input:focus {
  outline: none;
  border-color: rgb(131, 186, 38);
}

.footer-bottom {
  border-top: 1px solid #374151;
  padding-top: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-bottom-content {
  display: flex;
  align-items: center;
  gap: 24px;
}

.footer-bottom p {
  margin: 0;
  font-size: 14px;
  color: #9ca3af;
}

.legal-links {
  display: flex;
  gap: 16px;
}

.legal-links a {
  color: #9ca3af;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.legal-links a:hover {
  color: rgb(131, 186, 38);
}

.social-links {
  display: flex;
  align-items: center;
  gap: 12px;
}

.social-links span {
  font-size: 14px;
  color: #9ca3af;
}

.social-links a {
  color: #9ca3af;
  transition: color 0.3s ease;
}

.social-links a:hover {
  color: rgb(131, 186, 38);
}

.social-links svg {
  width: 20px;
  height: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .desktop-nav {
    display: none;
  }

  .mobile-nav {
    display: block;
  }

  .hero-title {
    font-size: 36px;
    line-height: 40px;
  }

  .section-description,
  .goal-description,
  .community-subheading {
    font-size: 28px;
    line-height: 32px;
  }

  .goal-cards {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .footer-main {
    grid-template-columns: 1fr;
    gap: 32px;
  }

  .footer-links {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .newsletter-form {
    flex-direction: column;
  }

  .navigation {
    top: 0;
    padding: 12px 0;
  }

  .nav-container {
    padding: 0 16px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 28px;
    line-height: 32px;
  }

  .section-description,
  .goal-description,
  .community-subheading {
    font-size: 24px;
    line-height: 28px;
  }

  .btn-large {
    padding: 12px 24px;
    font-size: 16px;
  }

  .goal-card {
    padding: 24px;
  }

  .card-content {
    padding: 24px;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Glassmorphism Base Styles */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.glass-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Innovation Ecosystem Section */
.innovation-ecosystem-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

.innovation-ecosystem-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e2e8f0" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.ecosystem-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 50px;
  position: relative;
  z-index: 1;
}

.ecosystem-card {
  padding: 40px 30px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.ecosystem-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(131, 186, 38, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.ecosystem-card:hover::before {
  opacity: 1;
}

.card-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #83ba26, #3b82f6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  position: relative;
  z-index: 2;
}

.ecosystem-card h3 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #1e293b;
  position: relative;
  z-index: 2;
}

.ecosystem-card p {
  color: #64748b;
  margin-bottom: 25px;
  line-height: 1.6;
  position: relative;
  z-index: 2;
}

.card-button {
  background: linear-gradient(135deg, #83ba26, #3b82f6);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.card-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(131, 186, 38, 0.3);
}

/* Platform Features Carousel Section */
.platform-features-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.platform-features-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(131, 186, 38, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);
}

.features-header {
  text-align: center;
  margin-bottom: 60px;
  position: relative;
  z-index: 1;
}

.features-carousel {
  position: relative;
  z-index: 1;
}

.carousel-container {
  overflow: hidden;
  border-radius: 16px;
}

.carousel-track {
  display: flex;
  transition: transform 0.5s ease;
}

.feature-slide {
  min-width: 100%;
  padding: 0 20px;
}

.feature-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 40px;
  text-align: center;
  max-width: 600px;
  margin: 0 auto;
}

.floating-icon {
  width: 100px;
  height: 100px;
  margin: 0 auto 30px;
  background: linear-gradient(135deg, rgba(131, 186, 38, 0.2), rgba(59, 130, 246, 0.2));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #83ba26;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.feature-card h3 {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 20px;
  color: white;
}

.feature-card > p {
  color: #cbd5e1;
  margin-bottom: 30px;
  font-size: 16px;
  line-height: 1.6;
}

.feature-list {
  display: grid;
  gap: 15px;
  text-align: left;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #e2e8f0;
}

.feature-check {
  width: 24px;
  height: 24px;
  background: linear-gradient(135deg, #83ba26, #3b82f6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.carousel-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-top: 40px;
}

.carousel-btn {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.carousel-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.carousel-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.carousel-indicators {
  display: flex;
  gap: 8px;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: #83ba26;
  transform: scale(1.2);
}

.explore-features-btn {
  margin-top: 50px;
  display: block;
  margin-left: auto;
  margin-right: auto;
}

/* Our Impact Section */
.our-impact-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
}

.impact-header {
  text-align: center;
  margin-bottom: 60px;
}

.impact-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-bottom: 50px;
}

.stat-card {
  padding: 40px 30px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(131, 186, 38, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, #83ba26, #3b82f6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  position: relative;
  z-index: 2;
}

.stat-content {
  position: relative;
  z-index: 2;
}

.stat-number {
  display: flex;
  align-items: baseline;
  justify-content: center;
  margin-bottom: 15px;
}

.stat-number .number {
  font-size: 48px;
  font-weight: 700;
  color: #1e293b;
  line-height: 1;
}

.stat-number .suffix {
  font-size: 32px;
  font-weight: 600;
  color: #83ba26;
  margin-left: 4px;
}

.stat-card h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #1e293b;
}

.stat-card p {
  color: #64748b;
  line-height: 1.5;
}

.get-started-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 auto;
}

/* News & Updates Section */
.news-updates-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
  position: relative;
}

.news-updates-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 70%, rgba(131, 186, 38, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 30%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);
}

.news-header {
  text-align: center;
  margin-bottom: 50px;
  position: relative;
  z-index: 1;
}

.featured-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: rgba(131, 186, 38, 0.2);
  color: #83ba26;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 20px;
}

.news-articles {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 50px;
  position: relative;
  z-index: 1;
}

.news-article {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  padding: 30px;
  position: relative;
  overflow: hidden;
}

.news-article::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(131, 186, 38, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.news-article:hover::before {
  opacity: 1;
}

.article-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  background: #83ba26;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  z-index: 2;
}

.article-header {
  position: relative;
  z-index: 2;
  margin-bottom: 20px;
}

.article-category {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #83ba26;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 15px;
}

.news-article h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 10px;
  color: white;
  line-height: 1.3;
}

.news-article time {
  color: #94a3b8;
  font-size: 14px;
}

.news-article p {
  color: #cbd5e1;
  line-height: 1.6;
  margin-bottom: 20px;
  position: relative;
  z-index: 2;
}

.read-more-btn {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.read-more-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: #83ba26;
}

.view-all-link {
  display: inline-flex;
  align-items: center;
  color: #83ba26;
  text-decoration: none;
  font-weight: 500;
  margin: 0 auto;
  position: relative;
  z-index: 1;
  transition: color 0.3s ease;
}

.view-all-link:hover {
  color: #a3d154;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ecosystem-cards,
  .impact-stats,
  .news-articles {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .feature-card {
    padding: 30px 20px;
  }

  .carousel-controls {
    gap: 15px;
  }

  .carousel-btn {
    width: 40px;
    height: 40px;
  }

  .stat-number .number {
    font-size: 36px;
  }

  .stat-number .suffix {
    font-size: 24px;
  }
}
</style>
