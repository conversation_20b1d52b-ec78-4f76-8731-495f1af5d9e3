/**
 * Unified Authentication Service
 * 
 * Centralized service for handling all authentication dialogs and flows
 * across the entire application to avoid duplication and ensure consistency.
 */

import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useNotificationStore } from '@/stores/notifications'
import { useCategoryService, type CategoryOption } from './categoryService'

export interface AuthDialogState {
  isSignInOpen: boolean
  isSignUpOpen: boolean
  isEmailPasswordSignInOpen: boolean
  isEmailPasswordSignUpOpen: boolean

  // Multi-step sign-up states
  isMultiStepSignUpOpen: boolean
  isCategorySelectionOpen: boolean
  isSignInMethodSelectionOpen: boolean
  isPostRegistrationOnboardingOpen: boolean

  // Flow data
  selectedCategory: CategoryOption | null
  signUpStep: 'category' | 'method' | 'form' | 'onboarding'

  loading: boolean
  isPwd: boolean
}

export interface AuthFormData {
  email: string
  password: string
  acceptTerms?: boolean
  selectedCategory?: CategoryOption | null
}

// Global state for authentication dialogs
const authDialogState = ref<AuthDialogState>({
  isSignInOpen: false,
  isSignUpOpen: false,
  isEmailPasswordSignInOpen: false,
  isEmailPasswordSignUpOpen: false,

  // Multi-step sign-up states
  isMultiStepSignUpOpen: false,
  isCategorySelectionOpen: false,
  isSignInMethodSelectionOpen: false,
  isPostRegistrationOnboardingOpen: false,

  // Flow data
  selectedCategory: null,
  signUpStep: 'category',

  loading: false,
  isPwd: true
})

// Form data
const signInForm = ref<AuthFormData>({
  email: '',
  password: ''
})

const signUpForm = ref<AuthFormData>({
  email: '',
  password: '',
  acceptTerms: false,
  selectedCategory: null
})

// Form validation rules
export const emailRules = [
  (val: string) => !!val || 'Email is required',
  (val: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) || 'Please enter a valid email'
]

export const passwordRules = [
  (val: string) => !!val || 'Password is required',
  (val: string) => val.length >= 6 || 'Password must be at least 6 characters'
]

/**
 * Unified Authentication Service
 */
export function useUnifiedAuth() {
  const router = useRouter()
  const authStore = useAuthStore()

  // Computed properties
  const isAuthenticated = computed(() => authStore.isAuthenticated)
  const state = computed(() => authDialogState.value)
  const signInFormData = computed(() => signInForm.value)
  const signUpFormData = computed(() => signUpForm.value)

  // Dialog control functions
  const openSignInDialog = () => {
    closeAllDialogs()
    authDialogState.value.isSignInOpen = true
  }

  const openSignUpDialog = () => {
    closeAllDialogs()
    authDialogState.value.isSignUpOpen = true
  }

  const openEmailPasswordSignIn = () => {
    closeAllDialogs()
    authDialogState.value.isEmailPasswordSignInOpen = true
  }

  const openEmailPasswordSignUp = () => {
    closeAllDialogs()
    authDialogState.value.isEmailPasswordSignUpOpen = true
  }

  const closeAllDialogs = () => {
    authDialogState.value.isSignInOpen = false
    authDialogState.value.isSignUpOpen = false
    authDialogState.value.isEmailPasswordSignInOpen = false
    authDialogState.value.isEmailPasswordSignUpOpen = false

    // Close multi-step dialogs
    authDialogState.value.isMultiStepSignUpOpen = false
    authDialogState.value.isCategorySelectionOpen = false
    authDialogState.value.isSignInMethodSelectionOpen = false
    authDialogState.value.isPostRegistrationOnboardingOpen = false

    // Reset flow data
    authDialogState.value.selectedCategory = null
    authDialogState.value.signUpStep = 'category'

    authDialogState.value.loading = false
  }

  // Dialog switching functions
  const switchToSignUp = () => {
    authDialogState.value.isSignInOpen = false
    authDialogState.value.isSignUpOpen = true
  }

  const switchToSignIn = () => {
    authDialogState.value.isSignUpOpen = false
    authDialogState.value.isSignInOpen = true
  }

  const switchToSignUpForm = () => {
    authDialogState.value.isEmailPasswordSignInOpen = false
    authDialogState.value.isEmailPasswordSignUpOpen = true
  }

  const switchToSignInForm = () => {
    authDialogState.value.isEmailPasswordSignUpOpen = false
    authDialogState.value.isEmailPasswordSignInOpen = true
  }

  // Multi-step sign-up functions
  const openMultiStepSignUp = () => {
    closeAllDialogs()
    authDialogState.value.isMultiStepSignUpOpen = true
    authDialogState.value.isCategorySelectionOpen = true
    authDialogState.value.signUpStep = 'category'
  }

  const selectCategory = (category: CategoryOption) => {
    const { saveSelectedCategory } = useCategoryService()
    authDialogState.value.selectedCategory = category
    saveSelectedCategory(category.id)
  }

  const proceedToMethodSelection = () => {
    authDialogState.value.isCategorySelectionOpen = false
    authDialogState.value.isSignInMethodSelectionOpen = true
    authDialogState.value.signUpStep = 'method'
  }

  const proceedToRegistrationForm = () => {
    authDialogState.value.isSignInMethodSelectionOpen = false
    authDialogState.value.isEmailPasswordSignUpOpen = true
    authDialogState.value.signUpStep = 'form'
  }

  const showPostRegistrationOnboarding = () => {
    closeAllDialogs()
    authDialogState.value.isPostRegistrationOnboardingOpen = true
    authDialogState.value.signUpStep = 'onboarding'
  }

  const backToCategorySelection = () => {
    authDialogState.value.isSignInMethodSelectionOpen = false
    authDialogState.value.isCategorySelectionOpen = true
    authDialogState.value.signUpStep = 'category'
  }

  const backToMethodSelection = () => {
    authDialogState.value.isEmailPasswordSignUpOpen = false
    authDialogState.value.isSignInMethodSelectionOpen = true
    authDialogState.value.signUpStep = 'method'
  }

  // Auth option handlers
  const handleEmailPasswordSignIn = () => {
    authDialogState.value.isSignInOpen = false
    authDialogState.value.isEmailPasswordSignInOpen = true
  }

  const handleEmailPasswordSignUp = () => {
    authDialogState.value.isSignUpOpen = false
    authDialogState.value.isEmailPasswordSignUpOpen = true
  }

  // Form submission handlers
  const handleSignIn = async () => {
    authDialogState.value.loading = true
    try {
      await authStore.signIn(signInForm.value.email, signInForm.value.password)
      closeAllDialogs()
      // Success notification is handled in the auth store
      router.push('/dashboard')
    } catch (error: any) {
      console.error('Sign in error:', error)
      // Error notification is handled in the auth store
    } finally {
      authDialogState.value.loading = false
    }
  }

  const handleSignUp = async () => {
    authDialogState.value.loading = true
    try {
      // Validate terms acceptance
      if (!signUpForm.value.acceptTerms) {
        const notifications = useNotificationStore()
        notifications.error('You must accept the Terms and Conditions to create an account.')
        return
      }

      const result = await authStore.signUp({
        email: signUpForm.value.email,
        password: signUpForm.value.password
      })

      // Only proceed to onboarding if signup was successful
      if (result && result.user) {
        // Check if this is part of multi-step flow or unified modal
        if (authDialogState.value.isMultiStepSignUpOpen || authDialogState.value.isSignUpOpen) {
          // Show post-registration onboarding
          showPostRegistrationOnboarding()
        } else {
          // Traditional flow - close dialogs and redirect
          closeAllDialogs()
          router.push('/dashboard')
        }
      }
      // If result is null, signup failed and error was already handled in auth store

      // Success notification is handled in the auth store
    } catch (error: any) {
      console.error('Sign up error:', error)
      // The auth store throws errors with specific messages, we need to display them
      const notifications = useNotificationStore()
      notifications.error(error.message || 'Registration failed. Please try again.')
    } finally {
      authDialogState.value.loading = false
    }
  }

  // Form data management
  const updateSignInForm = (field: keyof AuthFormData, value: string) => {
    (signInForm.value as any)[field] = value
  }

  const updateSignUpForm = (field: keyof AuthFormData, value: string | boolean | CategoryOption | null) => {
    (signUpForm.value as any)[field] = value
  }

  const resetForms = () => {
    signInForm.value = { email: '', password: '' }
    signUpForm.value = { email: '', password: '', acceptTerms: false, selectedCategory: null }
  }

  const togglePasswordVisibility = () => {
    authDialogState.value.isPwd = !authDialogState.value.isPwd
  }

  // Public API
  return {
    // State
    state,
    isAuthenticated,
    signInFormData,
    signUpFormData,

    // Dialog controls
    openSignInDialog,
    openSignUpDialog,
    openEmailPasswordSignIn,
    openEmailPasswordSignUp,
    closeAllDialogs,

    // Multi-step sign-up controls
    openMultiStepSignUp,
    selectCategory,
    proceedToMethodSelection,
    proceedToRegistrationForm,
    showPostRegistrationOnboarding,
    backToCategorySelection,
    backToMethodSelection,

    // Dialog switching
    switchToSignUp,
    switchToSignIn,
    switchToSignUpForm,
    switchToSignInForm,

    // Auth option handlers
    handleEmailPasswordSignIn,
    handleEmailPasswordSignUp,

    // Form handlers
    handleSignIn,
    handleSignUp,

    // Form data management
    updateSignInForm,
    updateSignUpForm,
    resetForms,
    togglePasswordVisibility,

    // Validation rules
    emailRules,
    passwordRules
  }
}

/**
 * Global authentication dialog trigger functions
 * These can be called from anywhere in the app
 */
export const triggerSignIn = () => {
  const { openSignInDialog } = useUnifiedAuth()
  openSignInDialog()
}

export const triggerSignUp = () => {
  const { openSignUpDialog } = useUnifiedAuth()
  openSignUpDialog()
}

export const triggerMultiStepSignUp = () => {
  const { openMultiStepSignUp } = useUnifiedAuth()
  openMultiStepSignUp()
}
